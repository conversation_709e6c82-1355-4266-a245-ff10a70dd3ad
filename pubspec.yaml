name: odigo_offline
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.3.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  freezed_annotation: 2.4.1
  json_annotation: 4.8.1
  injectable: 2.3.1
  get_it: 7.6.4
  kiosk_mode: 0.5.0
  flutter_riverpod: 2.4.8
  path_provider: 2.1.1
  cached_network_image: 3.3.0
  flutter_svg_custom:
    git:
      url: https://github.com/Mann97/flutter_svg_custom
  lottie: 1.4.2
  open_settings_plus: ^0.3.3
  easy_localization:
  flutter_screenutil: 5.9.0
  responsive_builder: 0.7.0
  dio: 5.3.3
  flutter_offline: 3.0.0
  pin_code_fields: 8.0.1
  carousel_slider:
  chewie: 1.8.5
  video_player: 2.9.1
  objectbox: 4.0.1
  objectbox_flutter_libs: 4.0.1
  qr_flutter: ^4.1.0
  get_thumbnail_video:
  flutter_downloader: ^1.11.7
  loading_animation_widget: ^1.2.1
  alarm: ^3.1.5
  volume_controller: ^2.0.8
  flutter_colorpicker: ^1.1.0
  collection:
  pointycastle: ^3.8.0
  basic_utils: ^5.5.4
  audioplayers: ^6.1.0
  uuid: ^4.5.1
  encrypt: ^5.0.3
  image: ^4.3.0
  syncfusion_flutter_gauges: 21.1.39
  zoom_widget: ^2.0.1
  archive: ^3.6.1
  permission_handler: ^11.3.1
  square_percent_indicater: ^0.1.0
  flutter_image_compress: ^2.3.0
  easy_debounce: ^2.0.3
  http:
  device_info_plus:


dev_dependencies:
  flutter_test:
    sdk: flutter


  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints:
  build_runner:
  freezed:
  json_serializable:
  injectable_generator:
  flutter_gen_runner: 5.3.2
  lints:
  socket_io_client: 1.0.0
  #  socket_io_client: ^2.0.3+1
  objectbox_generator: any

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter_gen:
  output: lib/ui/utils/theme
  integrations:
    flutter_svg: true
    flutter_custom_svg: true
    lottie: true

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
#    - assets/images/hsbc/
    - assets/json/
    - assets/fonts/
    - assets/svgs/
    - lang/en.json
    - assets/animation/
    - lib/framework/utils/encryption/YXBwX3B1YmxpYw_pu.md
    - lib/framework/utils/encryption/YXBwX3ByaXZhdGU_pr.md

  fonts:
    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit/Outfit-Black.ttf
        - asset: assets/fonts/Outfit/Outfit-Bold.ttf
        - asset: assets/fonts/Outfit/Outfit-ExtraBold.ttf
        - asset: assets/fonts/Outfit/Outfit-ExtraLight.ttf
        - asset: assets/fonts/Outfit/Outfit-Light.ttf
        - asset: assets/fonts/Outfit/Outfit-Medium.ttf
        - asset: assets/fonts/Outfit/Outfit-Regular.ttf
        - asset: assets/fonts/Outfit/Outfit-SemiBold.ttf
        - asset: assets/fonts/Outfit/Outfit-Thin.ttf

    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope/Manrope-Bold.ttf
        - asset: assets/fonts/Manrope/Manrope-ExtraBold.ttf
        - asset: assets/fonts/Manrope/Manrope-ExtraLight.ttf
        - asset: assets/fonts/Manrope/Manrope-Light.ttf
        - asset: assets/fonts/Manrope/Manrope-Medium.ttf
        - asset: assets/fonts/Manrope/Manrope-Regular.ttf
        - asset: assets/fonts/Manrope/Manrope-SemiBold.ttf

    - family: Nebula
      fonts:
        - asset: assets/fonts/Nebula/Nebula-Regular.otf
