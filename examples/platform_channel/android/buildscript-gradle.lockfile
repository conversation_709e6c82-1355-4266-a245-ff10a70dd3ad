# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
androidx.databinding:databinding-common:7.2.0=classpath
androidx.databinding:databinding-compiler-common:7.2.0=classpath
com.android.databinding:baseLibrary:7.2.0=classpath
com.android.tools.analytics-library:crash:30.2.0=classpath
com.android.tools.analytics-library:protos:30.2.0=classpath
com.android.tools.analytics-library:shared:30.2.0=classpath
com.android.tools.analytics-library:tracker:30.2.0=classpath
com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09=classpath
com.android.tools.build.jetifier:jetifier-processor:1.0.0-beta09=classpath
com.android.tools.build:aapt2-proto:7.2.0-7984345=classpath
com.android.tools.build:aaptcompiler:7.2.0=classpath
com.android.tools.build:apksig:7.2.0=classpath
com.android.tools.build:apkzlib:7.2.0=classpath
com.android.tools.build:builder-model:7.2.0=classpath
com.android.tools.build:builder-test-api:7.2.0=classpath
com.android.tools.build:builder:7.2.0=classpath
com.android.tools.build:bundletool:1.8.2=classpath
com.android.tools.build:gradle-api:7.2.0=classpath
com.android.tools.build:gradle:7.2.0=classpath
com.android.tools.build:manifest-merger:30.2.0=classpath
com.android.tools.build:transform-api:2.0.0-deprecated-use-gradle-api=classpath
com.android.tools.ddms:ddmlib:30.2.0=classpath
com.android.tools.layoutlib:layoutlib-api:30.2.0=classpath
com.android.tools.lint:lint-model:30.2.0=classpath
com.android.tools.lint:lint-typedef-remover:30.2.0=classpath
com.android.tools.utp:android-device-provider-ddmlib-proto:30.2.0=classpath
com.android.tools.utp:android-device-provider-gradle-proto:30.2.0=classpath
com.android.tools.utp:android-test-plugin-host-additional-test-output-proto:30.2.0=classpath
com.android.tools.utp:android-test-plugin-host-coverage-proto:30.2.0=classpath
com.android.tools.utp:android-test-plugin-host-retention-proto:30.2.0=classpath
com.android.tools.utp:android-test-plugin-result-listener-gradle-proto:30.2.0=classpath
com.android.tools:annotations:30.2.0=classpath
com.android.tools:common:30.2.0=classpath
com.android.tools:dvlib:30.2.0=classpath
com.android.tools:repository:30.2.0=classpath
com.android.tools:sdk-common:30.2.0=classpath
com.android.tools:sdklib:30.2.0=classpath
com.android:signflinger:7.2.0=classpath
com.android:zipflinger:7.2.0=classpath
com.fasterxml.jackson.core:jackson-annotations:2.11.1=classpath
com.fasterxml.jackson.core:jackson-core:2.11.1=classpath
com.fasterxml.jackson.core:jackson-databind:2.11.1=classpath
com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.11.1=classpath
com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.11.1=classpath
com.fasterxml.jackson.module:jackson-module-kotlin:2.11.1=classpath
com.fasterxml.woodstox:woodstox-core:6.2.1=classpath
com.github.gundy:semver4j:0.16.4=classpath
com.google.android:annotations:*******=classpath
com.google.api.grpc:proto-google-common-protos:1.12.0=classpath
com.google.auto.value:auto-value-annotations:1.6.2=classpath
com.google.code.findbugs:jsr305:3.0.2=classpath
com.google.code.gson:gson:2.8.6=classpath
com.google.crypto.tink:tink:1.3.0-rc2=classpath
com.google.dagger:dagger:2.28.3=classpath
com.google.errorprone:error_prone_annotations:2.3.4=classpath
com.google.flatbuffers:flatbuffers-java:1.12.0=classpath
com.google.guava:failureaccess:1.0.1=classpath
com.google.guava:guava:30.1-jre=classpath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=classpath
com.google.j2objc:j2objc-annotations:1.3=classpath
com.google.jimfs:jimfs:1.1=classpath
com.google.protobuf:protobuf-java-util:3.10.0=classpath
com.google.protobuf:protobuf-java:3.10.0=classpath
com.google.testing.platform:core-proto:0.0.8-alpha07=classpath
com.googlecode.json-simple:json-simple:1.1=classpath
com.googlecode.juniversalchardet:juniversalchardet:1.0.3=classpath
com.squareup:javapoet:1.10.0=classpath
com.squareup:javawriter:2.5.0=classpath
com.sun.activation:javax.activation:1.2.0=classpath
com.sun.istack:istack-commons-runtime:3.0.8=classpath
com.sun.xml.fastinfoset:FastInfoset:1.2.16=classpath
commons-codec:commons-codec:1.11=classpath
commons-io:commons-io:2.4=classpath
commons-logging:commons-logging:1.2=classpath
de.undercouch:gradle-download-task:4.1.1=classpath
io.grpc:grpc-api:1.21.1=classpath
io.grpc:grpc-context:1.21.1=classpath
io.grpc:grpc-core:1.21.1=classpath
io.grpc:grpc-netty:1.21.1=classpath
io.grpc:grpc-protobuf-lite:1.21.1=classpath
io.grpc:grpc-protobuf:1.21.1=classpath
io.grpc:grpc-stub:1.21.1=classpath
io.netty:netty-buffer:4.1.34.Final=classpath
io.netty:netty-codec-http2:4.1.34.Final=classpath
io.netty:netty-codec-http:4.1.34.Final=classpath
io.netty:netty-codec-socks:4.1.34.Final=classpath
io.netty:netty-codec:4.1.34.Final=classpath
io.netty:netty-common:4.1.34.Final=classpath
io.netty:netty-handler-proxy:4.1.34.Final=classpath
io.netty:netty-handler:4.1.34.Final=classpath
io.netty:netty-resolver:4.1.34.Final=classpath
io.netty:netty-transport:4.1.34.Final=classpath
io.opencensus:opencensus-api:0.21.0=classpath
io.opencensus:opencensus-contrib-grpc-metrics:0.21.0=classpath
it.unimi.dsi:fastutil:8.4.0=classpath
jakarta.activation:jakarta.activation-api:1.2.1=classpath
jakarta.xml.bind:jakarta.xml.bind-api:2.3.2=classpath
javax.inject:javax.inject:1=classpath
net.java.dev.jna:jna-platform:5.6.0=classpath
net.java.dev.jna:jna:5.6.0=classpath
net.sf.jopt-simple:jopt-simple:4.9=classpath
net.sf.kxml:kxml2:2.3.0=classpath
org.apache.commons:commons-compress:1.20=classpath
org.apache.httpcomponents:httpclient:4.5.9=classpath
org.apache.httpcomponents:httpcore:4.4.11=classpath
org.apache.httpcomponents:httpmime:4.5.6=classpath
org.bitbucket.b_c:jose4j:0.7.0=classpath
org.bouncycastle:bcpkix-jdk15on:1.56=classpath
org.bouncycastle:bcprov-jdk15on:1.56=classpath
org.checkerframework:checker-qual:3.5.0=classpath
org.codehaus.mojo:animal-sniffer-annotations:1.17=classpath
org.codehaus.woodstox:stax2-api:4.2.1=classpath
org.glassfish.jaxb:jaxb-runtime:2.3.2=classpath
org.glassfish.jaxb:txw2:2.3.2=classpath
org.jdom:jdom2:2.0.6=classpath
org.jetbrains.dokka:dokka-core:1.4.32=classpath
org.jetbrains.intellij.deps:trove4j:1.0.20181211=classpath
org.jetbrains.kotlin:kotlin-android-extensions:1.5.31=classpath
org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.5.31=classpath
org.jetbrains.kotlin:kotlin-build-common:1.5.31=classpath
org.jetbrains.kotlin:kotlin-compiler-embeddable:1.5.31=classpath
org.jetbrains.kotlin:kotlin-compiler-runner:1.5.31=classpath
org.jetbrains.kotlin:kotlin-daemon-client:1.5.31=classpath
org.jetbrains.kotlin:kotlin-daemon-embeddable:1.5.31=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-api:1.5.31=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-model:1.5.31=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.31=classpath
org.jetbrains.kotlin:kotlin-klib-commonizer-api:1.5.31=classpath
org.jetbrains.kotlin:kotlin-native-utils:1.5.31=classpath
org.jetbrains.kotlin:kotlin-project-model:1.5.31=classpath
org.jetbrains.kotlin:kotlin-reflect:1.5.31=classpath
org.jetbrains.kotlin:kotlin-scripting-common:1.5.31=classpath
org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:1.5.31=classpath
org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:1.5.31=classpath
org.jetbrains.kotlin:kotlin-scripting-jvm:1.5.31=classpath
org.jetbrains.kotlin:kotlin-stdlib-common:1.5.31=classpath
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.5.31=classpath
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.31=classpath
org.jetbrains.kotlin:kotlin-stdlib:1.5.31=classpath
org.jetbrains.kotlin:kotlin-tooling-metadata:1.5.31=classpath
org.jetbrains.kotlin:kotlin-util-io:1.5.31=classpath
org.jetbrains.kotlin:kotlin-util-klib:1.5.31=classpath
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.0=classpath
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.0=classpath
org.jetbrains:annotations:13.0=classpath
org.jetbrains:markdown-jvm:0.2.1=classpath
org.jetbrains:markdown:0.2.1=classpath
org.json:json:20180813=classpath
org.jsoup:jsoup:1.13.1=classpath
org.jvnet.staxex:stax-ex:1.8.1=classpath
org.ow2.asm:asm-analysis:9.1=classpath
org.ow2.asm:asm-commons:9.1=classpath
org.ow2.asm:asm-tree:9.1=classpath
org.ow2.asm:asm-util:9.1=classpath
org.ow2.asm:asm:9.1=classpath
org.slf4j:slf4j-api:1.7.30=classpath
org.tensorflow:tensorflow-lite-metadata:0.1.0-rc2=classpath
xerces:xercesImpl:2.12.0=classpath
xml-apis:xml-apis:1.4.01=classpath
empty=
