# Example of embedding Flutter using FlutterView

This project demonstrates how to embed Flutter within an iOS or Android
application. On iOS, the iOS and Flutter components are built with Xcode. On
Android, the Android and Flutter components are built with Android Studio or
Gradle.

You can read more about
[accessing platform and third-party services in Flutter](https://flutter.dev/platform-services/).

## iOS

You can open `ios/Runner.xcworkspace` in Xcode and build the project as
usual.

## Android

You can open `android/` in Android Studio and build the project as usual.
