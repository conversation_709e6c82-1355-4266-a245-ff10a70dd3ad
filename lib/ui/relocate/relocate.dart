import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'mobile/relocate_mobile.dart';

class Relocate extends StatelessWidget {
  const Relocate({Key? key}) : super(key: key);

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
        mobile: (BuildContext context) {
          return const RelocateMobile();
        }
    );
  }
}

