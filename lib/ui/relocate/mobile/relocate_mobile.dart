import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_point_model.dart';
import 'package:odigo_offline/framework/controller/locations/locations_controller.dart';
import 'package:odigo_offline/framework/controller/new_dashboard/new_dashboard_controller.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/new_dashboard/custom_keyboard/artistic_multilingual_keyboard.dart';
import 'package:odigo_offline/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_form_field.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';

class RelocateMobile extends ConsumerStatefulWidget {
  const RelocateMobile({super.key});

  @override
  ConsumerState<RelocateMobile> createState() => _RelocateMobileState();
}

class _RelocateMobileState extends ConsumerState<RelocateMobile> {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      hideBottomMenu();
      final locationWatch = ref.read(locationsController);
      await locationWatch.getLocationPointList();
      locationWatch.updateSelectedLocationPoint(null);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final locationWatch = ref.watch(locationsController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// top close icon
            const Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CommonBackWidget(
                  color: AppColors.white,
                ),
              ],
            ),
            SizedBox(height: 15.h),

            /// stores
            CommonText(
              title: LocaleKeys.keyRelocate.localized,
              textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp),
            ),
            SizedBox(height: 17.h),

            /// explore nearby stores
            CommonText(
              title: LocaleKeys.keyLocationsList.localized,
              textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp),
            ),
            SizedBox(height: 38.h),

            CommonInputFormField(
              textEditingController: locationWatch.searchLocationController,
              focus: locationWatch.searchLocationFocus,
              validator: (str) => null,
              readOnly: true,
              cursorColor: AppColors.white,
              hintText: 'Search Location',
              hintTextStyle: TextStyles.medium.copyWith(color: AppColors.white.withOpacity(0.5)),
              borderRadius: BorderRadius.circular(25.r),
              onTap: () {
                locationWatch.searchLocationFocus.requestFocus();
                locationWatch.notifyListeners();
              },
            ).paddingOnly(bottom: 20.h),
            if (locationWatch.searchLocationFocus.hasFocus)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(23.r),
                  color: AppColors.black.withOpacity(0.5),
                ),
                height: context.height * 0.2,
                alignment: Alignment.center,
                child: KeyboardLayouts(
                  showGlassKeyboard: true,
                  textEditingController: locationWatch.searchLocationController,
                  focusNode: locationWatch.searchLocationFocus,
                  enableLanguageButton: false,
                  keyShadowColor: Colors.black,
                  keyboardBackgroundColor: AppColors.transparent,
                  keysBackgroundColor: AppColors.black,
                  keyTextStyle: TextStyle(color: AppColors.white, fontSize: 22.sp),
                  keyBorderRadius: BorderRadius.circular(12.r),
                  showDoneButton: true,
                  currentKeyboardLanguage: KeyboardLanguages.english,
                  onKeyPressed: () {
                    locationWatch.filterLocations(searchKeyword: locationWatch.searchLocationController.text);
                    locationWatch.searchLocationFocus.requestFocus();
                  },
                  keyboardActionDoneEvent: () {
                    FocusScope.of(context).unfocus();
                  },
                ),
              ).paddingOnly(bottom: 20.h),
            Container(
              height: context.height * 0.05,
              decoration: BoxDecoration(
                color: AppColors.clr212121,
                borderRadius: BorderRadius.circular(35.r),
                border: Border.all(
                  color: AppColors.clr3A3A3A,
                ),
              ),
              child: Row(
                children: List.generate(
                  locationWatch.locationTypeList.length,
                  (index) {
                    LocationPointType locationType = locationWatch.locationTypeList[index];
                    return Expanded(
                      child: ListBounceAnimation(
                        onTap: () {
                          locationWatch.updateSelectedLocationType(locationType);
                        },
                        child: Center(
                          child: CommonText(
                            title: locationType.name.capitalizeFirstLetterOfSentence,
                            textStyle: TextStyles.semiBold.copyWith(
                              fontSize: 40.sp,
                              color: locationType == locationWatch.selectedLocationType ? AppColors.white : AppColors.clr545454,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ).paddingOnly(bottom: 20.h),

            /// route list
            Expanded(
              child: FadeBoxTransition(
                delay: 200,
                child: SingleChildScrollView(
                  padding: EdgeInsets.zero,
                  child: Column(
                    children: [
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemCount: locationWatch.filterLocationsList.length,
                        itemBuilder: (context, index) {
                          LocationPoint? locationPoint = locationWatch.filterLocationsList[index];
                          return Container(
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(50.r), border: Border.all(width: 1.w, color: AppColors.grey3A3A3A), color: AppColors.clr212121),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      CupertinoIcons.location_solid,
                                      color: AppColors.white,
                                      size: context.width * 0.05,
                                    ),
                                    SizedBox(width: 30.w),
                                    CommonText(
                                      title: locationPoint.name ?? '',
                                      textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 46.sp),
                                    ),
                                  ],
                                ),
                                // CommonSVG(
                                //   strIcon: locationWatch.selectedLocationPoint == locationPoint ? Assets.svgs.svgSelectedRoute.keyName : Assets.svgs.svgUnselectedRoute.keyName,
                                //   height: 50.h,
                                // )
                                ListBounceAnimation(
                                  onTap: () async {
                                    showConfirmationDialog(
                                      context,
                                      'Relocate',
                                      message: 'Do you want to relocate to ${locationPoint.name}',
                                      didTakeAction: (isPositive) {
                                        if (isPositive) {
                                          RobotEventManger.instance.relocateByPointName(locationPoint.name!);
                                        }
                                      },
                                    );
                                  },
                                  child: Container(
                                    width: context.width * 0.24,
                                    height: context.height * 0.06,
                                    decoration: BoxDecoration(
                                      color: AppColors.clr009AF1,
                                      borderRadius: BorderRadius.circular(65.r),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        CommonSVG(strIcon: Assets.svgs.svgReload.keyName, width: context.width * 0.03, height: context.width * 0.03),
                                        SizedBox(width: 10.w),
                                        CommonText(
                                          title: LocaleKeys.keyRelocate.localized.toUpperCase(),
                                          textStyle: TextStyles.semiBold.copyWith(
                                            fontSize: 36.sp,
                                            color: AppColors.white,
                                            fontFamily: TextStyles.manropeFontFamily,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ).paddingSymmetric(horizontal: context.width * 0.04, vertical: context.height * 0.025),
                          ).paddingOnly(bottom: 30.h);
                        },
                      ),
                      SizedBox(
                        height: 100.h,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // CommonButton(
            //   buttonText: locationWatch.relocateLoader ? 'Please Wait...' : LocaleKeys.keyRelocate.localized,
            //   buttonTextColor: AppColors.black,
            //   backgroundColor: AppColors.white,
            //   onTap: () async {
            //     if (locationWatch.relocateLoader == false) {
            //       if (locationWatch.selectedLocationPoint?.name == null && locationWatch.selectedLocationPoint?.name != '') {
            //         showCommonErrorDialog(context: context, message: 'Please select location');
            //       } else {
            //         locationWatch.updateRelocateLoader();
            //         await Future.delayed(const Duration(seconds: 8), () async {
            //           await newDashboardWatch.socketManager.relocate(locationWatch.selectedLocationPoint?.name);
            //           if (locationWatch.relocateSuccess) {
            //             locationWatch.updateRelocateLoader();
            //             await showCommonErrorDialog(context: context, message: 'Relocate Successfully!');
            //             ref.read(navigationStackController).pop();
            //             locationWatch.updateRelocate();
            //           } else {
            //             locationWatch.updateRelocateLoader();
            //             showCommonErrorDialog(context: context, message: 'Not Relocate');
            //           }
            //         });
            //       }
            //     }
            //   },
            // ).paddingSymmetric(vertical: 30.h),
          ],
        ),
      ),
    );
  }
}
