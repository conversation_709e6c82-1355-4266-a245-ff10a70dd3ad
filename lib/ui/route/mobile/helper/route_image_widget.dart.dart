import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/route/route_controller.dart';

import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';

import 'package:odigo_offline/ui/asset_master/mobile/helper/common_asset_tile.dart';
import 'package:odigo_offline/ui/default_settings/mobile/helper/common_add_asset_widget_mobile.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';

import 'package:odigo_offline/ui/select_asset_view/mobile/select_asset_view_mobile.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';

class RouteImageWidget extends ConsumerWidget with BaseConsumerWidget {
  const RouteImageWidget({super.key});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final routeWatch = ref.watch(routeController);
    return SizedBox(
      child: GridView.builder(
        padding: EdgeInsets.zero,
        itemCount: (routeWatch.routeAssetModel?.image.length ?? 0),
        itemBuilder: (context, index) {
          Asset? asset = routeWatch.routeAssetModel?.image[index];
          return asset != null
              ? CommonAssetTile(
                  asset,
                  key: ValueKey(asset.assetUuid ?? ''),
                  onPreviewTap: () {
                    ref.read(navigationStackController).push(NavigationStackItem.assetPreview(assetList: routeWatch.routeAssetModel?.image ?? [], index: index));
                  },
                  onDeleteTap: () {
                    showConfirmationDialog(
                      context,
                      'Delete Asset',
                      message: 'Are you sure you want to remove this asset from route',
                      didTakeAction: (isPositive) {
                        if (isPositive) {
                          routeWatch.routeAssetModel?.image.removeWhere((element) => element.id == asset.id);
                          routeWatch.updateRouteAsset(type: AssetType.image);
                        }
                      },
                    );
                  },
                )
              : const Offstage();
        },
        gridDelegate: getDelegate(context, mainAxisExtent: context.height * 0.25),
      ).paddingSymmetric(horizontal: 40.w, vertical: 20.h),
    );
  }
}
