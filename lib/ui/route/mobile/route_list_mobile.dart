import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_offline/framework/controller/hardware_controller/hardware_controller.dart';
import 'package:odigo_offline/framework/controller/route/route_controller.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/route/mobile/helper/route_list_offline.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:odigo_offline/ui/utils/widgets/empty_state_widget.dart';

class RouteListMobile extends ConsumerStatefulWidget {
  const RouteListMobile({super.key});

  @override
  ConsumerState createState() => _RouteListMobileState();
}

class _RouteListMobileState extends ConsumerState<RouteListMobile> {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      await hideBottomMenu();
      final routeWatch = ref.read(routeController);
      await routeWatch.disposeLocationForm();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final routeWatch = ref.watch(routeController);
    return 1 == 1
        ? const RouteListOffline()
        : Scaffold(
            backgroundColor: AppColors.primary,
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// top close icon
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      CommonBackWidget(
                        color: AppColors.white,
                      ),
                    ],
                  ),
                  SizedBox(height: 15.h),

                  CommonText(
                    title: LocaleKeys.keyROUTELIST.localized,
                    textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp),
                  ),
                  SizedBox(height: 17.h),

                  CommonText(
                    title: LocaleKeys.keyViewAvailableRoutes.localized,
                    textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp),
                  ),
                  SizedBox(height: 38.h),

                  /// route list
                  Expanded(
                    child: routeWatch.routeList.isEmpty
                        ? const EmptyStateWidget()
                        : FadeBoxTransition(
                            delay: 200,
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  ListView.builder(
                                    physics: const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: routeWatch.routeList.length,
                                    itemBuilder: (context, index) {
                                      String route = routeWatch.routeList[index];
                                      return ListBounceAnimation(
                                        onTap: () {
                                          routeWatch.updateRoute(route);
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(50.r), border: Border.all(width: 1.w, color: AppColors.grey3A3A3A), color: AppColors.clr212121),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  CommonText(
                                                    title: route,
                                                    textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 46.sp),
                                                  ),
                                                  CommonSVG(
                                                    strIcon: route.toUpperCase() == (routeWatch.selectedRoute ?? '').toUpperCase() ? Assets.svgs.svgSelectedRoute.keyName : Assets.svgs.svgRadioUnselected.keyName,
                                                    height: 50.h,
                                                  )
                                                ],
                                              ),
                                            ],
                                          ).paddingSymmetric(horizontal: context.width * 0.04, vertical: context.height * 0.03),
                                        ).paddingOnly(bottom: 30.h),
                                      );
                                    },
                                  ),
                                  SizedBox(
                                    height: 100.h,
                                  )
                                ],
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
            bottomNavigationBar: Visibility(
              visible: routeWatch.routeList.isNotEmpty,
              child: CommonButton(
                onTap: () async {
                  if (ref.read(hardwareController).isEmergency) {
                    showMessageDialog(
                      context,
                      message: 'Please Remove emergency first.',
                    );
                  } else {
                    // await ref.read(displayAdsScreenController).startRoute(routeName: routeWatch.selectedRoute ?? '');
                    ref.read(navigationStackController).popUntil(const NavigationStackItem.displayAds());
                  }
                },
                height: context.height * 0.07,
                backgroundColor: AppColors.white,
                buttonText: 'Start Cruise Mode',
                buttonTextColor: AppColors.black,
              ).paddingLTRB(context.width * 0.05, 0, context.width * 0.05, context.height * 0.05),
            ),
          );
  }
}
