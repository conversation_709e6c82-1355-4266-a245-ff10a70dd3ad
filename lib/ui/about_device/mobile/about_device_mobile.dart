import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/about_device/about_device_controller.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/model/robot_meta_data_model.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/datetime_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/about_device/mobile/helper/common_text_widget.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';

class AboutDeviceMobile extends ConsumerWidget with BaseConsumerWidget {
  const AboutDeviceMobile({super.key});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    ref.watch(aboutDeviceController);
    RobotMetaData? robotData = RobotMetaDataRepository.instance.robotData;
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
        child: Column(
          children: [
            const Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CommonBackWidget(
                  color: AppColors.white,
                ),
              ],
            ),

            ///title
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonText(
                      title: LocaleKeys.keyInformation.localized,
                      textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 46.sp),
                    ),
                    SizedBox(height: 8.h),
                    CommonText(
                      title: 'Last Updated : ${robotData?.timeStamp ?? DateTime.now().dateOnly}',
                      textStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 30.sp),
                    ),
                  ],
                ).paddingSymmetric(vertical: 35.h),
              ],
            ),
            SizedBox(
              height: 20.h,
            ),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      children: [
                        CommonTextWidget('App Version', '${robotData?.sessionData.target?.apkVersion}'.toUpperCase()),
                        CommonTextWidget('Host Ip', RobotMetaDataRepository.instance.hostIpAddress ?? 'N/A'),
                        CommonTextWidget('Host Wifi-Name', RobotMetaDataRepository.instance.hostWifiName ?? 'N/A'),
                        CommonTextWidget('Android Ip', RobotMetaDataRepository.instance.androidIpAddress ?? 'N/A'),
                        CommonTextWidget('Android Wifi-Name', RobotMetaDataRepository.instance.androidWifiName ?? 'N/A'),
                        CommonTextWidget('Android Id', RobotMetaDataRepository.instance.androidId ?? 'N/A'),
                        CommonTextWidget('Emergency', '${robotData?.sessionData.target?.isEmergencyPressed}'.toUpperCase()),
                        CommonTextWidget('Navigation Speed', '${(RobotMetaDataRepository.instance.navigationSpeed * 100).toInt()}%'),
                        CommonTextWidget('Cruise Speed', '${(RobotMetaDataRepository.instance.cruiseSpeed * 100).toInt()}%'),
                        CommonTextWidget('System Volume', '${(RobotMetaDataRepository.instance.systemVolume * 100).toInt()}%'),
                        CommonTextWidget(LocaleKeys.keyHostName.localized, robotData?.sessionData.target?.hostname ?? 'N/A', isCopy: true),
                        CommonTextWidget(LocaleKeys.keyNavigationVersion.localized, robotData?.versionData.target?.navigationVersion ?? 'N/A', isCopy: true),
                        CommonTextWidget(LocaleKeys.keyPowerBoardVersion.localized, robotData?.versionData.target?.powerboardVersion ?? 'N/A', isCopy: true),
                        CommonTextWidget('Firmware Version', robotData?.versionData.target?.powerboardFirmwareVersion ?? 'N/A', isCopy: true),
                        CommonTextWidget('Loader Version', robotData?.versionData.target?.loaderVersion ?? 'N/A', isCopy: true),
                      ],
                    ),
                  ),
                  SizedBox(height: context.height * 0.01),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 100.h,
                          decoration: BoxDecoration(
                            color: robotData?.sensorData.target?.isLidarOk == true ? AppColors.clr60A80D : AppColors.darkRed,
                            borderRadius: BorderRadius.circular(50.r),
                          ),
                          child: Center(
                            child: CommonText(
                              title: 'Lidar',
                              textStyle: TextStyles.medium.copyWith(fontSize: 24.sp, color: AppColors.white),
                            ),
                          ),
                        ).paddingOnly(right: 20.h),
                      ),
                      Expanded(
                        child: Container(
                          height: 100.h,
                          decoration: BoxDecoration(
                            color: robotData?.sensorData.target?.isImuOk == true ? AppColors.clr60A80D : AppColors.darkRed,
                            borderRadius: BorderRadius.circular(50.r),
                          ),
                          child: Center(
                            child: CommonText(
                              title: 'IMU',
                              textStyle: TextStyles.medium.copyWith(fontSize: 24.sp, color: AppColors.white),
                            ),
                          ),
                        ).paddingOnly(right: 20.h),
                      ),
                      Expanded(
                        child: Container(
                          height: 100.h,
                          decoration: BoxDecoration(
                            color: robotData?.sensorData.target?.isOdomOk == true ? AppColors.clr60A80D : AppColors.darkRed,
                            borderRadius: BorderRadius.circular(50.r),
                          ),
                          child: Center(
                            child: CommonText(
                              title: 'Odo',
                              textStyle: TextStyles.medium.copyWith(fontSize: 24.sp, color: AppColors.white),
                            ),
                          ),
                        ).paddingOnly(right: 20.h),
                      ),
                      Expanded(
                        child: Container(
                          height: 100.h,
                          decoration: BoxDecoration(
                            color: robotData?.sensorData.target?.is3dCameraOk == true ? AppColors.clr60A80D : AppColors.darkRed,
                            borderRadius: BorderRadius.circular(50.r),
                          ),
                          child: Center(
                            child: CommonText(
                              title: '3D Camera',
                              textStyle: TextStyles.medium.copyWith(fontSize: 24.sp, color: AppColors.white),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
