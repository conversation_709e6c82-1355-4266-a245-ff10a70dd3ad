import 'dart:math';

import 'package:flutter/scheduler.dart';
import 'package:flutter_svg_custom/flutter_svg.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';

class ControlBarSlider extends StatelessWidget {
  final double value;
  final double min;
  final double max;
  final DrawableRoot? bottomIcon;
  final ValueChanged<double>? onChanged;
  final int percentageValue;
  final String suffixString;
  final ValueChanged<double>? onChangeEnd;

  const ControlBarSlider({
    super.key,
    required this.value,
    required this.min,
    required this.max,
    this.onChanged,
    required this.percentageValue,
    this.suffixString = '%',
    this.onChangeEnd,
    this.bottomIcon,
  });

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        trackHeight: context.height * 0.05,
        thumbShape: SliderComponentShape.noThumb,
        overlayShape: SliderComponentShape.noOverlay,
        trackShape: CustomRoundedRectSliderTrackShape(value, max, bottomIcon, percentageValue, suffixString),
        activeTrackColor: AppColors.white,
        inactiveTrackColor: AppColors.clr212121,
        inactiveTickMarkColor: AppColors.transparent,
        activeTickMarkColor: AppColors.transparent,
        disabledActiveTickMarkColor: AppColors.transparent,
        disabledInactiveTickMarkColor: AppColors.transparent,
      ),
      child: Slider(
        value: value,
        min: min,
        max: max,
        onChanged: onChanged,
        onChangeEnd: onChangeEnd,
      ),
    );
  }
}

class CustomRoundedRectSliderTrackShape extends SliderTrackShape with BaseSliderTrackShape {
  final double value;
  final double max;
  final DrawableRoot? image;
  final int? percentage;
  final String suffixString;

  /// Create a slider track that draws two rectangles with rounded outer edges.
  const CustomRoundedRectSliderTrackShape(this.value, this.max, this.image, this.percentage, this.suffixString);

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 0,
  }) {
    assert(sliderTheme.disabledActiveTrackColor != null);
    assert(sliderTheme.disabledInactiveTrackColor != null);
    assert(sliderTheme.activeTrackColor != null);
    assert(sliderTheme.inactiveTrackColor != null);
    assert(sliderTheme.thumbShape != null);

    if (sliderTheme.trackHeight == null || sliderTheme.trackHeight! <= 0) {
      return;
    }

    final ColorTween activeTrackColorTween = ColorTween(begin: sliderTheme.disabledActiveTrackColor, end: AppColors.clr009AF1);
    final ColorTween inactiveTrackColorTween = ColorTween(begin: sliderTheme.disabledInactiveTrackColor, end: sliderTheme.inactiveTrackColor);
    final Paint activePaint = Paint()..color = activeTrackColorTween.evaluate(enableAnimation)!;
    final Paint inactivePaint = Paint()..color = inactiveTrackColorTween.evaluate(enableAnimation)!;
    final (Paint leftTrackPaint, Paint rightTrackPaint) = switch (textDirection) {
      TextDirection.ltr => (activePaint, inactivePaint),
      TextDirection.rtl => (inactivePaint, activePaint),
    };

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );
    final Radius trackRadius = Radius.circular(trackRect.height / 4);
    final Radius activeTrackRadius = Radius.circular((trackRect.height + additionalActiveTrackHeight) / 4);
    context.canvas.drawRRect(
      RRect.fromLTRBAndCorners(
        trackRect.left,
        (textDirection == TextDirection.rtl) ? trackRect.top - (additionalActiveTrackHeight / 2) : trackRect.top,
        trackRect.right,
        (textDirection == TextDirection.rtl) ? trackRect.bottom + (additionalActiveTrackHeight / 2) : trackRect.bottom,
        topRight: (textDirection == TextDirection.rtl) ? activeTrackRadius : trackRadius,
        bottomRight: (textDirection == TextDirection.rtl) ? activeTrackRadius : trackRadius,
        topLeft: (textDirection == TextDirection.rtl) ? activeTrackRadius : trackRadius,
        bottomLeft: (textDirection == TextDirection.rtl) ? activeTrackRadius : trackRadius,
      ),
      rightTrackPaint,
    );
    context.canvas.drawRRect(
      RRect.fromLTRBAndCorners(
        trackRect.left,
        (textDirection == TextDirection.ltr) ? trackRect.top - (additionalActiveTrackHeight / 2) : trackRect.top,
        thumbCenter.dx,
        (textDirection == TextDirection.ltr) ? trackRect.bottom + (additionalActiveTrackHeight / 2) : trackRect.bottom,
        topLeft: (textDirection == TextDirection.ltr) ? activeTrackRadius : trackRadius,
        bottomLeft: (textDirection == TextDirection.ltr) ? activeTrackRadius : trackRadius,
        topRight: (textDirection == TextDirection.ltr) ? activeTrackRadius : trackRadius,
        bottomRight: (textDirection == TextDirection.ltr) ? activeTrackRadius : trackRadius,
        // topRight: (textDirection == TextDirection.ltr)
        //     ? (value > max - (max / 10))
        //         ? activeTrackRadius
        //         : Radius.zero
        //     : trackRadius,
        // bottomRight: (textDirection == TextDirection.ltr)
        //     ? (value > max - (max / 10))
        //         ? activeTrackRadius
        //         : Radius.zero
        //     : trackRadius,
      ),
      leftTrackPaint,
    );
    var xPoint = globalContext!.width * 0.05;
    var yPoint = globalContext!.height * 0.025;
    if (image != null) {
      double imageWidth = globalContext!.height * 0.025;
      double imageHeight = globalContext!.height * 0.025;
      context.canvas.save();

      // Move canvas to the correct position
      context.canvas.translate(xPoint, yPoint);

      // Scale the image to fit the fixed size
      double scaleX = imageWidth / image!.viewport.width;
      double scaleY = imageHeight / image!.viewport.height;
      double scale = min(scaleX, scaleY); // Keep aspect ratio if needed

      context.canvas.scale(scale, scale);

      // Center the drawable image
      context.canvas.translate(-image!.viewport.width / 2, -image!.viewport.height / 2);

      // Draw the SVG
      image!.draw(context.canvas, Rect.zero);

      context.canvas.restore();
    }
    if (percentage != null) {
      xPoint = globalContext!.width * 0.4;
      yPoint = globalContext!.width * 0.02;
      // Draw total text
      TextSpan totalMemoryText = TextSpan(style: TextStyles.bold.copyWith(fontFamily: TextStyles.manropeFontFamily, fontSize: 50.sp, color: AppColors.white), text: '$percentage$suffixString');
      TextPainter tmPainter = TextPainter(text: totalMemoryText, textAlign: TextAlign.center, textDirection: TextDirection.ltr);
      tmPainter.layout();
      context.canvas.translate(xPoint, yPoint);
      tmPainter.paint(context.canvas, const Offset(0, 0));
      context.canvas.translate(-xPoint, -yPoint);
    }
  }
}
