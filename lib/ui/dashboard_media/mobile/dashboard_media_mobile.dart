import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/framework/controller/default_settings/default_settings_controller.dart';
import 'package:odigo_offline/ui/dashboard_media/mobile/helper/default_dashboard_media_widget_mobile.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/select_asset_view/mobile/select_asset_view_mobile.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';

class DashboardMediaMobile extends ConsumerStatefulWidget {
  const DashboardMediaMobile({super.key});

  @override
  ConsumerState<DashboardMediaMobile> createState() => _DashboardMediaMobileState();
}

class _DashboardMediaMobileState extends ConsumerState<DashboardMediaMobile> with BaseConsumerStatefulWidget {
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final defaultSettingsWatch = ref.read(defaultSettingsController);
      defaultSettingsWatch.disposeController(isNotify: true);
      await defaultSettingsWatch.getDefaultDashboardMediaSettings();
    });
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    final defaultSettingsWatch = ref.watch(defaultSettingsController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                title: LocaleKeys.keyMediaSetting.localized,
                textStyle: TextStyles.regular.copyWith(
                  fontSize: 50.sp,
                  fontFamily: TextStyles.nebulaFontFamily,
                ),
              ),
              CommonBackWidget(
                onTap: () {
                  ref.read(navigationStackController).pop();
                },
                color: AppColors.white,
              ),
            ],
          ).paddingSymmetric(horizontal: 40.w, vertical: 10.h),
          SizedBox(height: 40.h),
          Row(
            children: [
              Expanded(
                child: Container(
                  width: context.width * 0.6,
                  height: context.height * 0.05,
                  decoration: BoxDecoration(
                    color: AppColors.clr212121,
                    borderRadius: BorderRadius.circular(35.r),
                    border: Border.all(
                      color: AppColors.clr3A3A3A,
                    ),
                  ),
                  child: Row(
                    children: List.generate(
                      defaultSettingsWatch.dashboardMediaType.length,
                      (index) {
                        return Expanded(
                          child: ListBounceAnimation(
                            onTap: () {
                              defaultSettingsWatch.updateSelectedMediaIndex(index);
                            },
                            child: Center(
                              child: CommonText(
                                title: defaultSettingsWatch.dashboardMediaType[index].localized,
                                textStyle: TextStyles.semiBold.copyWith(
                                  fontSize: 40.sp,
                                  color: index == (defaultSettingsWatch.selectedMediaIndex) ? AppColors.white : AppColors.clr545454,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              if (defaultSettingsWatch.selectedDefaultSettingsTabIndex != 2) SizedBox(width: context.width * 0.02),
              if (defaultSettingsWatch.selectedDefaultSettingsTabIndex != 2)
                CommonButton(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (BuildContext context) {
                          return SelectAssetViewMobile(
                            assetType: defaultSettingsWatch.selectedMediaIndex == 0 ? AssetType.video : AssetType.image,
                            selectionCallBack: (value) {
                              Navigator.pop(context);
                              defaultSettingsWatch.updateDefaultDashboardMediaSettings(context, asset: value.first);
                            },
                            assetList: defaultSettingsWatch.dashboardMediaModel?.asset.target != null ? [defaultSettingsWatch.dashboardMediaModel!.asset.target!] : [],
                            isMultiSelection: false,
                          );
                        },
                        fullscreenDialog: true,
                      ),
                    );
                  },
                  width: context.width * 0.2,
                  height: context.height * 0.05,
                  buttonText: 'Select',
                  backgroundColor: AppColors.grey2B2B2B,
                ),
            ],
          ),
          SizedBox(height: context.height * 0.03),
          const Expanded(
            flex: 4,
            child: DefaultDashboardMediaWidgetMobile(),
          ),
        ],
      ).paddingSymmetric(horizontal: context.width * 0.05, vertical: context.height * 0.03),
    );
  }
}
