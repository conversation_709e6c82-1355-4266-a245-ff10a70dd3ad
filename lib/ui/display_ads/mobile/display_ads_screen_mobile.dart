import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/default_settings/default_settings_controller.dart';
import 'package:odigo_offline/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';

import 'package:odigo_offline/ui/display_ads/mobile/helper/chargin_screen_widget.dart';
import 'package:odigo_offline/ui/display_ads/mobile/helper/help_info_dashboard_widget.dart';
import 'package:odigo_offline/ui/display_ads/mobile/helper/hsbc_options_dialog.dart';
import 'package:odigo_offline/ui/display_ads/mobile/helper/touch_animation_widget.dart';
import 'package:odigo_offline/ui/new_dashboard/helper/cruise_pause_screen.dart';
import 'package:odigo_offline/ui/new_dashboard/helper/login_dialog_widget.dart';
import 'package:odigo_offline/ui/new_dashboard/helper/pause_screen.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/dialog_progressbar.dart';

class DisplayAdsScreenMobile extends ConsumerStatefulWidget {
  const DisplayAdsScreenMobile({super.key});

  @override
  ConsumerState<DisplayAdsScreenMobile> createState() => _DisplayAdsScreenMobileState();
}

class _DisplayAdsScreenMobileState extends ConsumerState<DisplayAdsScreenMobile> with BaseConsumerStatefulWidget, WidgetsBindingObserver, RouteAware {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      WidgetsBinding.instance.addObserver(this);
      final displayAdsScreenWatch = ref.read(displayAdsScreenController);
      displayAdsScreenWatch.disposeController(isNotify: true);
      displayAdsScreenWatch.updateIsLoading(true);
      await displayAdsScreenWatch.getDefaultSettings();
      await ref.read(defaultSettingsController).getDefaultDashboardMediaSettings();
      displayAdsScreenWatch.updateIsLoading(false);
      displayAdsScreenWatch.playDefaultVideo();
      if (displayAdsScreenWatch.displayMode == DisplayMode.CRUISE) {
        displayAdsScreenWatch.startRouteNavigation();
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.black,
      body: displayAdsScreenWatch.isLoading
          ? const DialogProgressBar()
          : Stack(
              children: [
                mainDisplayBodyWidget(),

                const HelpInfoDashboardWidget(),
                if (RobotMetaDataRepository.instance.enableHsbcMode && getEnvironment() == Env.hsbc)
                  Positioned(
                    left: 25.w,
                    right: 25.w,
                    top: 30.h,
                    child: TouchAnimationWidget(
                      isShowUpperWidget: true,
                      onTap: () {
                        if (RobotMetaDataRepository.instance.enableHsbcMode && getEnvironment() == Env.hsbc) {
                          displayAdsScreenWatch.showHsbcDialog(context);
                          return;
                        }
                      },
                    ),
                  ),
                if (RobotMetaDataRepository.instance.enableHsbcMode && getEnvironment() == Env.hsbc)
                  Positioned(
                    left: 25.w,
                    right: 25.w,
                    bottom: 30.h,
                    child: TouchAnimationWidget(
                      onTap: () {
                        if (RobotMetaDataRepository.instance.enableHsbcMode && getEnvironment() == Env.hsbc) {
                          displayAdsScreenWatch.showHsbcDialog(context);
                          return;
                        }
                      },
                    ),
                  ),

                /// authentication login dialog
                Positioned(left: MediaQuery.sizeOf(context).width * 0.1, right: MediaQuery.sizeOf(context).width * 0.1, top: MediaQuery.sizeOf(context).height * 0.05, child: const LoginDialogWidget()),

                ///Pause cruise mode Screen
                const CruisePauseScreen(),

                ///Pause Navigation Screen
                const PauseScreen(),

                ///Emergency Stop Screen
                // const EmergencyScreen(),
                const ChargingScreenWidget(),
              ],
            ),
    );
  }

  Widget mainDisplayBodyWidget() {
    final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
    return GestureDetector(
      onLongPress: () {
        displayAdsScreenWatch.stopMediaPlayback();
        ref.read(navigationStackController).push(const NavigationStackItem.newDashboard());
      },
      onTap: () async {
        displayAdsScreenWatch.actionOpenHelper(context);
      },
      onDoubleTap: () async {
        if (displayAdsScreenWatch.receivedNavigationEventType != ReceivedNavigationEventType.none) {
          switch (displayAdsScreenWatch.displayMode) {
            case DisplayMode.CRUISE || DisplayMode.NAVIGATION:
              await displayAdsScreenWatch.robotEventManger.pauseNavigation();
              displayAdsScreenWatch.changeCruisePauseNav(true);
              displayAdsScreenWatch.pauseMediaPlayback();
              break;
            case DisplayMode.TOUR:
              await displayAdsScreenWatch.robotEventManger.pauseNavigation();
              displayAdsScreenWatch.changePauseNav(true);
              displayAdsScreenWatch.pauseMediaPlayback();
              break;
          }
        }
      },
      child: (displayAdsScreenWatch.playingMediaType == PlayingMediaType.video
          ? videoPlayer()
          : displayAdsScreenWatch.playingMediaType == PlayingMediaType.audio
              ? imagePlayer()
              : thumbnailWidget()),
    );
  }

  /// Video Player
  Widget videoPlayer({String? path}) {
    final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
    return SizedBox(
      height: context.height,
      width: context.width,
      child: Center(
        child: displayAdsScreenWatch.chewieController != null && (displayAdsScreenWatch.chewieController?.videoPlayerController.value.isInitialized ?? false)
            ? Chewie(
                controller: displayAdsScreenWatch.chewieController!,
              )
            : defaultThumbnailWidget(path: path),
      ),
    );
  }

  /// Video Player
  Widget imagePlayer() {
    final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
    if (displayAdsScreenWatch.imageFile == null) {
      return thumbnailWidget();
    }
    return Image.file(
      File(displayAdsScreenWatch.imageFile.decryptedFilePath),
      height: MediaQuery.sizeOf(context).height,
      width: MediaQuery.sizeOf(context).width,
      gaplessPlayback: true,
      fit: BoxFit.cover,
    );
  }

  Widget thumbnailWidget() {
    final displayAdsScreenWatch = ref.watch(defaultSettingsController);
    if (displayAdsScreenWatch.dashboardMediaModel?.asset.target == null) {
      return defaultThumbnailWidget();
    } else {
      File file = File(displayAdsScreenWatch.dashboardMediaModel?.asset.target?.assetPath ?? '');
      if (file.existsSync()) {
        if (displayAdsScreenWatch.dashboardMediaModel?.asset.target?.assetType?.toLowerCase() == AssetType.image.name.toLowerCase()) {
          return defaultThumbnailWidget(path: file.path);
        } else {
          return videoPlayer(path: displayAdsScreenWatch.dashboardMediaModel?.asset.target?.assetThumbnail);
        }
      } else {
        return defaultThumbnailWidget();
      }
    }
  }

  Widget defaultThumbnailWidget({String? path}) {
    if (path != null) {
      return Image.file(
        File(path),
        height: MediaQuery.sizeOf(context).height,
        width: MediaQuery.sizeOf(context).width,
        gaplessPlayback: true,
        fit: BoxFit.cover,
      );
    }
    return Image.asset(
      'assets/images/img.png',
      height: MediaQuery.sizeOf(context).height,
      width: MediaQuery.sizeOf(context).width,
      gaplessPlayback: true,
      fit: BoxFit.cover,
    );
  }
}
