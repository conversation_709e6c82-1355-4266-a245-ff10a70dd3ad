import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/framework/utils/extension/int_extension.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:square_percent_indicater/square_percent_indicater.dart';

class ReachedToLocationDialog extends ConsumerWidget {
  const ReachedToLocationDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(45.r)),
      child: SquarePercentIndicator(
        borderRadius: 45.r,
        progressColor: AppColors.clr009AF1,
        shadowColor: AppColors.greyF7F7F7,
        progressWidth: context.width * 0.01,
        shadowWidth: context.width * 0.01,
        height: context.height * 0.25,
        width: context.width,
        progress: ((displayAdsScreenWatch.navigationWaitingTimer?.tick ?? 0)) / (RobotMetaDataRepository.instance.navigationWaitingTime.milliseconds),
        child: Container(
          height: context.height * 0.25,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(45.r),
          ),
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.02, vertical: context.height * 0.01),
          child: Column(
            children: [
              SizedBox(height: context.height * 0.02),
              CommonSVG(
                strIcon: Assets.svgs.svgNavigationSuccess.path,
                height: context.height * 0.07,
              ),
              SizedBox(height: context.height * 0.02),
              CommonText(
                title: 'Navigation Success',
                textStyle: TextStyles.bold.copyWith(color: AppColors.black, fontSize: 48.sp),
              ),
              SizedBox(height: context.height * 0.02),
              CommonText(
                title: 'You have arrived to the location ${displayAdsScreenWatch.selectedNavigationLocation?.locationName ?? ''}',
                textStyle: TextStyles.medium.copyWith(color: AppColors.black),
              ),
              CommonText(
                title: 'I will continue my cruise in ${(((RobotMetaDataRepository.instance.navigationWaitingTime.milliseconds) - (displayAdsScreenWatch.navigationWaitingTimer?.tick ?? 0)) / 1000).round()} seconds',
                textStyle: TextStyles.medium.copyWith(color: AppColors.black),
              ),
              SizedBox(height: context.height * 0.01),
            ],
          ),
        ),
      ),
    );
  }
}
