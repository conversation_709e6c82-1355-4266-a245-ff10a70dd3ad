import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/hardware_controller/hardware_controller.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/utils/anim/slide_up_transition.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';

class ChargingScreenWidget extends ConsumerWidget {
  const ChargingScreenWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hardwareWatch = ref.watch(hardwareController);
    return Positioned(
      bottom: 0,
      child: hardwareWatch.isCharging
          ? SlideUpTransition(
              child: Stack(
                children: [
                  Container(
                    height: context.height * 0.07,
                    width: context.width,
                    decoration: BoxDecoration(
                      color: AppColors.grey707070,
                      border: Border.all(color: AppColors.grey707070),
                    ),
                  ),
                  Positioned(
                    child: Container(
                      height: context.height * 0.07,
                      width: context.width * (hardwareWatch.batteryPercentage / 100),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomLeft,
                          end: Alignment.topRight,
                          colors: [
                            AppColors.grey707070,
                            AppColors.black,
                            AppColors.black,
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: context.height * 0.07,
                    width: context.width,
                    decoration: BoxDecoration(borderRadius: BorderRadius.only(topLeft: Radius.circular(25.r), bottomLeft: Radius.circular(25.r)), color: AppColors.transparent),
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CommonSVG(
                          strIcon: Assets.svgs.svgCharging.path,
                        ),
                        CommonText(
                          title: '${hardwareWatch.batteryPercentage} %',
                          textStyle: TextStyles.medium.copyWith(fontSize: 40.sp, color: AppColors.white),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          : const Offstage(),
    );
  }
}
