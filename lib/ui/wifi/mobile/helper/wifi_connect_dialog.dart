import 'package:odigo_offline/framework/controller/wifi/available_wifi_controller.dart';
import 'package:odigo_offline/framework/repository/wifi/wifi_model.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/utils/const/form_validations.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/widgets/common_form_field.dart';

class WifiConnectDialog extends ConsumerWidget {
  final WifiModel wifiModel;
  final Function? onButtonTap;
  final String? buttonText;

  const WifiConnectDialog({
    super.key,
    required this.wifiModel,
    this.onButtonTap,
    this.buttonText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(
      builder: (dialogContext, ref, child) {
        final wifiWatch = ref.watch(availableWifiController);
        return Column(
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(dialogContext);
              },
              child: Align(
                alignment: Alignment.topRight,
                child: CommonSVG(
                  strIcon: Assets.svgs.svgCrossRounded.keyName,
                  height: dialogContext.height * 0.017,
                  width: dialogContext.height * 0.017,
                ).paddingOnly(right: dialogContext.width * 0.020),
              ),
            ),
            Row(
              children: [
                CommonSVG(
                  strIcon: wifiSymbol(wifiModel.wifiStrength ?? 0, wifiModel.isSecured ?? false),
                  height: dialogContext.height * 0.050,
                  width: dialogContext.height * 0.050,
                ).paddingOnly(right: dialogContext.width * 0.020),
                CommonText(
                  title: wifiModel.deviceName ?? '',
                  textStyle: TextStyles.medium.copyWith(fontSize: 37.sp, color: AppColors.black),
                ),
              ],
            ).paddingOnly(bottom: dialogContext.height * 0.020),
            Form(
              key: wifiWatch.formKey,
              child: CommonInputFormField(
                // readOnly: true,
                textEditingController: wifiWatch.networkSecurityCtr,
                // textInputType: TextInputType.number,
                labelText: LocaleKeys.keyEnterTheNetworkSecurityKey.localized,
                fieldTextStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                labelTextStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                hintTextStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                validator: (value) {
                  return validateText(value, LocaleKeys.keySecurityKeyIsRequired.localized);
                },
              ).paddingOnly(bottom: dialogContext.height * 0.020),
            ),
            CommonButton(
              buttonText: buttonText ?? LocaleKeys.keyConnect.localized,
              width: dialogContext.width * 0.5,
              height: dialogContext.width * 0.1,
              onTap: () {
                if (wifiWatch.formKey.currentState?.validate() ?? false) {
                  Navigator.pop(dialogContext);
                  if (onButtonTap != null) {
                    onButtonTap?.call();
                  } else {
                    wifiWatch.authenticateWifi(context, ref, wifiModel.deviceName ?? '');
                  }
                }
              },
            )
          ],
        );
      },
    );
  }
}
