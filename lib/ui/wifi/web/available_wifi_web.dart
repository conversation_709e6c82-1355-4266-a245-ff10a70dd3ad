import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/scheduler.dart';

class AvailableWifiWeb extends ConsumerStatefulWidget {
  const AvailableWifiWeb({Key? key}) : super(key: key);

  @override
  ConsumerState<AvailableWifiWeb> createState() => _AvailableWifiWebState();
}

class _AvailableWifiWebState extends ConsumerState<AvailableWifiWeb> {

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      // final availableWifiWatch = ref.watch(availableWifiController);
      //availableWifiWatch.disposeController(isNotify : true);
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    return Container();
  }


}
