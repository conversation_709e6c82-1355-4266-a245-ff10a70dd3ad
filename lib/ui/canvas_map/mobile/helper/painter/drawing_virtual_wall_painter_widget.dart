import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_controller.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_painter_controller.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'dart:ui' as ui;

class DrawingVirtualWallPainterWidget extends ConsumerWidget {
  const DrawingVirtualWallPainterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('DrawingVirtualWallPainterWidget');
    final canvasPaintWatch = ref.watch(drawingVirtualWallPainterController);
    final canvasMapWatch = ref.watch(canvasMapController);
    return (canvasMapWatch.drawVirtualWall && !canvasMapWatch.eraseVirtualWall)
        ? CustomPaint(
            painter: canvasPaintWatch.drawingVirtualWallPainterCanvas,
            size: ui.Size(MapVariables.map.width, MapVariables.map.height),
          )
        : const Offstage();
  }
}
