import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_painter_controller.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'dart:ui' as ui;

class MapPainterWidget extends ConsumerWidget {
  const MapPainterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('MapPainterWidget');
    final canvasPaintWatch = ref.watch(mapPainterController);

    return CustomPaint(
      painter: canvasPaintWatch.mapPainterCanvas,
      size: ui.Size(MapVariables.map.width, MapVariables.map.height),
    );
  }
}
