import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_controller.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_painter_controller.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'dart:ui' as ui;

class ContinousPainterWidget extends ConsumerWidget {
  const ContinousPainterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('ContinousPainterWidget');
    final canvasPaintWatch = ref.watch(continousDataPainterController);
    return CustomPaint(
      painter: canvasPaintWatch.continousDataCanvas,
      size: ui.Size(MapVariables.map.width, MapVariables.map.height),
    );
  }
}
