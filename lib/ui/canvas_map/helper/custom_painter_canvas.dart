import 'dart:math';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_controller.dart';
import 'package:odigo_offline/framework/controller/canvas_map/graph_extension.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'package:odigo_offline/framework/repository/map/model/relocation_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/special_area_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/virtual_wall_response_model.dart';
import 'package:odigo_offline/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:flutter_svg_custom/flutter_svg.dart';

import 'dart:ui' as ui;

import 'package:odigo_offline/ui/utils/theme/theme.dart';

class MapPainterCanvas extends CustomPainter {
  final ui.Image image;
  final double scale;

  MapPainterCanvas({
    required this.image,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    Paint painter = Paint();
    painter.color = AppColors.clrB50000;
    painter.color = AppColors.newMapColor;
    if ((scale) > 3) {
      painter.imageFilter = ui.ImageFilter.blur(sigmaX: 0.4, sigmaY: 0.4);
      painter.blendMode = BlendMode.srcOver;
    } else {
      painter = ui.Paint()..color = AppColors.newMapColor;
    }
    canvas.drawImage(image, const Offset(0, 0), painter);
    painter = ui.Paint()..color = AppColors.newMapColor;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class PositionPainterCanvas extends CustomPainter {
  final DrawableRoot? odigoImage;
  final double scale;
  final PositionEvent? currentPosition;

  PositionPainterCanvas({
    this.odigoImage,
    required this.scale,
    this.currentPosition,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    if (odigoImage != null && currentPosition != null) {
      final xPoint = currentPosition!.x!.convertXFromDasherPoint;
      final yPoint = currentPosition!.y!.convertYFromDasherPoint;
      final Offset centerPosition = Offset(MapVariables.odigoWidth / 2, MapVariables.odigoHeight / 2);
      canvas.translate(xPoint - centerPosition.dx, yPoint - centerPosition.dy);
      canvas.translate(centerPosition.dx, centerPosition.dy);
      var thetaAngle = currentPosition!.theta! * (180 / pi);
      if (thetaAngle < 0) {
        thetaAngle += 360;
      }
      canvas.rotate((thetaAngle * (pi / 180)) * -1);
      final double inverseScale = 1.0 / (scale);
      canvas.scale(inverseScale);
      canvas.translate(-centerPosition.dx, -centerPosition.dy);
      odigoImage!.draw(canvas, Rect.zero);
      canvas.scale(1 / inverseScale);
      canvas.translate(-(xPoint - centerPosition.dx), -(yPoint - centerPosition.dy));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class WaypointsPainterCanvas extends CustomPainter {
  final DrawableRoot? chargingPointImage;
  final DrawableRoot? productionPointImage;
  final DrawableRoot? deliveryPointImage;
  final List<Waypoint>? wayPoints;
  final double scale;

  WaypointsPainterCanvas({
    this.chargingPointImage,
    this.productionPointImage,
    this.deliveryPointImage,
    this.wayPoints,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (wayPoints != null) {
      wayPoints?.forEach((points) {
        drawPoints(canvas, painter, size, double.parse((points.pose?.x).toString()), double.parse((points.pose?.y).toString()), points.name, pointTypeValues.map[points.type]);
      });
    }
  }

  void drawPoints(ui.Canvas canvas, Paint painter, ui.Size size, double dx, double dy, String name, PointType? type) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    Color textColor = Colors.black;
    double desiredWidth = (24.0 / ((1.2) - 0.5));
    double desiredHeight = (24.0 / ((1.2) - 0.5));
    if ((scale) > 1.2) {
      desiredWidth = (24.0 / ((scale) - 0.5));
      desiredHeight = (24.0 / ((scale) - 0.5));
    }
    canvas.translate(xPoint - (desiredWidth / 2), yPoint - desiredHeight);
    switch (type) {
      case PointType.PRODUCTION:
        textColor = const ui.Color(0xFFAFB42C);
        // Calculate scaling factors
        final scaleX = desiredWidth / productionPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / productionPointImage!.viewport.viewBoxRect.height;
        canvas.scale(scaleX, scaleY);
        productionPointImage!.draw(canvas, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas.scale((1 / scaleX), (1 / scaleY));
        break;
      case PointType.CHARGE:
        textColor = const ui.Color(0xFF03950C);
        final scaleX = desiredWidth / chargingPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / chargingPointImage!.viewport.viewBoxRect.height;
        canvas.scale(scaleX, scaleY);
        chargingPointImage!.draw(canvas, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas.scale((1 / scaleX), (1 / scaleY));
        break;
      default:
        textColor = const ui.Color(0xFF126EEf);
        final scaleX = desiredWidth / deliveryPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / deliveryPointImage!.viewport.viewBoxRect.height;
        canvas.scale(scaleX, scaleY);
        deliveryPointImage!.draw(canvas, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas.scale((1 / scaleX), (1 / scaleY));
        break;
    }
    canvas.translate((xPoint - (desiredWidth / 2)) * -1, (yPoint - (desiredHeight)) * -1);
    final ui.ParagraphBuilder paragraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(fontSize: (11 / (scale)), fontWeight: FontWeight.w800))
      ..pushStyle(ui.TextStyle(color: textColor))
      ..addText(name);
    final ui.Paragraph paragraph = paragraphBuilder.build()..layout(ui.ParagraphConstraints(width: (size.width ?? 0)));
    canvas.drawParagraph(paragraph, Offset(xPoint - (paragraph.longestLine / 2), yPoint + (5 / (scale)) / 2));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class RoutesPainterCanvas extends CustomPainter {
  final DrawableRoot? routeImage;
  final Map<String, List<List<double>>>? naviRoutes;
  final double scale;

  RoutesPainterCanvas({
    this.routeImage,
    this.naviRoutes,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (naviRoutes != null) {
      naviRoutes?.forEach((name, points) {
        drawNaviRoutes(canvas, painter, size, name, points);
      });
    }
  }

  void drawNaviRoutes(ui.Canvas canvas, Paint painter, ui.Size size, String name, List<List<double>> route) {
    painter.color = const ui.Color(0xFFBB7F4D);
    painter.strokeWidth = 1;
    for (var routePath in route) {
      canvas.drawCircle(Offset(routePath.first.convertXFromDasherPoint, routePath.last.convertYFromDasherPoint), 2 / (scale), painter);
    }
    int routePathIndex = 0;
    for (routePathIndex = 0; routePathIndex < route.length; routePathIndex++) {
      double x1Point = route[routePathIndex].first.convertXFromDasherPoint;
      double y1Point = route[routePathIndex].last.convertYFromDasherPoint;
      double x2Point = 0;
      double y2Point = 0;
      if (routePathIndex == (route.length - 1)) {
        x2Point = route[0].first.convertXFromDasherPoint;
        y2Point = route[0].last.convertYFromDasherPoint;
      } else {
        x2Point = route[routePathIndex + 1].first.convertXFromDasherPoint;
        y2Point = route[routePathIndex + 1].last.convertYFromDasherPoint;
        if (routePathIndex == 0) {
          final xPoint = route[routePathIndex].first.convertXFromDasherPoint;
          final yPoint = route[routePathIndex].last.convertYFromDasherPoint;
          Color textColor = Colors.black;
          double desiredWidth = (24.0 / ((1.2) - 0.5));
          double desiredHeight = (24.0 / ((1.2) - 0.5));
          if ((scale) > 1.2) {
            desiredWidth = (24.0 / ((scale) - 0.5));
            desiredHeight = (24.0 / ((scale) - 0.5));
          }
          canvas.translate(xPoint - (desiredWidth / 2), yPoint - desiredHeight);
          textColor = const ui.Color(0xFFBB7F4D);
          final scaleX = desiredWidth / routeImage!.viewport.viewBoxRect.width;
          final scaleY = desiredHeight / routeImage!.viewport.viewBoxRect.height;
          canvas.scale(scaleX, scaleY);
          routeImage!.draw(canvas, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
          canvas.scale((1 / scaleX), (1 / scaleY));
          canvas.translate((xPoint - (desiredWidth / 2)) * -1, (yPoint - (desiredHeight)) * -1);
          final ui.ParagraphBuilder paragraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(fontSize: (11 / (scale)), fontWeight: FontWeight.w800))
            ..pushStyle(ui.TextStyle(color: textColor))
            ..addText(name);
          final ui.Paragraph paragraph = paragraphBuilder.build()..layout(ui.ParagraphConstraints(width: (size.width ?? 0)));
          canvas.drawParagraph(paragraph, Offset(xPoint - (paragraph.longestLine / 2), yPoint + (5 / (scale)) / 2));
        }
        Offset end = ui.Offset(x2Point, y2Point);
        Offset start = ui.Offset(x1Point, y1Point);
        double totalDistance = (end - start).distance;
        double dx = (end.dx - start.dx) / totalDistance;
        double dy = (end.dy - start.dy) / totalDistance;

        double currentDistance = 0;
        double dashLength = 4;
        double dashGap = 2;

        while (currentDistance < totalDistance) {
          final double startX = start.dx + dx * currentDistance;
          final double startY = start.dy + dy * currentDistance;

          currentDistance += dashLength;

          if (currentDistance > totalDistance) {
            currentDistance = totalDistance;
          }

          final double endX = start.dx + dx * currentDistance;
          final double endY = start.dy + dy * currentDistance;

          canvas.drawLine(Offset(startX, startY), Offset(endX, endY), painter);

          currentDistance += dashGap;
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class VirtualWallPainterCanvas extends CustomPainter {
  final double scale;
  final List<VirtualWallPoint> virtualWalls;

  VirtualWallPainterCanvas({
    required this.virtualWalls,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    for (var walls in virtualWalls) {
      drawVirtualWall(canvas, painter, walls.pose.point1.x, walls.pose.point1.y, walls.pose.point2.x, walls.pose.point2.y);
    }
  }

  void drawVirtualWall(ui.Canvas canvas, ui.Paint painter, double dx1, double dy1, double dx2, double dy2) {
    final x1Point = dx1.convertXFromDasherPoint;
    final y1Point = dy1.convertYFromDasherPoint;
    final x2Point = dx2.convertXFromDasherPoint;
    final y2Point = dy2.convertYFromDasherPoint;
    painter.color = Colors.red;
    painter.strokeWidth = 2 / scale;
    canvas.drawLine(ui.Offset(x1Point, y1Point), ui.Offset(x2Point, y2Point), painter);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class DrawingVirtualWallPainterCanvas extends CustomPainter {
  final double scale;
  final VirtualWallPoint? currentlyDrawingVirtualWall;

  DrawingVirtualWallPainterCanvas({
    this.currentlyDrawingVirtualWall,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (currentlyDrawingVirtualWall != null) {
      drawStartAndEndOfVirtualWall(canvas, painter, currentlyDrawingVirtualWall!.pose);
    }
  }

  void drawStartAndEndOfVirtualWall(ui.Canvas canvas, ui.Paint painter, VirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x;
    final wallPoint1Y = virtualWallPose.point1.y;
    final wallPoint2X = virtualWallPose.point2.x;
    final wallPoint2Y = virtualWallPose.point2.y;
    painter.color = Colors.blue;
    painter.strokeWidth = 2 / scale;
    canvas.drawLine(ui.Offset(wallPoint1X, wallPoint1Y), ui.Offset(wallPoint2X, wallPoint2Y), painter);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class DrawnVirtualWallPainterCanvas extends CustomPainter {
  final double scale;
  final List<VirtualWallPoint>? drawnVirtualWalls;

  DrawnVirtualWallPainterCanvas({
    this.drawnVirtualWalls,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    drawnVirtualWalls?.forEach((walls) {
      drawUpdatedVirtualWalls(canvas, painter, walls.pose);
    });
  }

  void drawUpdatedVirtualWalls(ui.Canvas canvas, ui.Paint painter, VirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x.convertXFromDasherPoint;
    final wallPoint1Y = virtualWallPose.point1.y.convertYFromDasherPoint;
    final wallPoint2X = virtualWallPose.point2.x.convertXFromDasherPoint;
    final wallPoint2Y = virtualWallPose.point2.y.convertYFromDasherPoint;
    painter.strokeWidth = 1;
    painter.color = Colors.green;
    canvas.drawLine(ui.Offset(wallPoint1X, wallPoint1Y), ui.Offset(wallPoint2X, wallPoint2Y), painter);
    painter.color = Colors.blue;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class ErasedVirtualWallPainterCanvas extends CustomPainter {
  final double scale;
  final EraseVirtualWallPoint? erasedVirtualWallData;

  ErasedVirtualWallPainterCanvas({
    this.erasedVirtualWallData,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (erasedVirtualWallData != null) {
      drawErasedVirtualWall(canvas, painter, erasedVirtualWallData!.pose);
    }
  }

  void drawErasedVirtualWall(ui.Canvas canvas, ui.Paint painter, EraseVirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x;
    final wallPoint1Y = virtualWallPose.point1.y;
    final wallPoint2X = virtualWallPose.point2.x;
    final wallPoint2Y = virtualWallPose.point2.y;
    final wallPoint3X = virtualWallPose.point3.x;
    final wallPoint3Y = virtualWallPose.point3.y;
    final wallPoint4X = virtualWallPose.point4.x;
    final wallPoint4Y = virtualWallPose.point4.y;
    ui.Offset point1 = ui.Offset(wallPoint1X, wallPoint1Y);
    ui.Offset point2 = ui.Offset(wallPoint2X, wallPoint2Y);
    ui.Offset point3 = ui.Offset(wallPoint3X, wallPoint3Y);
    ui.Offset point4 = ui.Offset(wallPoint4X, wallPoint4Y);
    painter.color = Colors.blue;
    canvas.drawCircle(point1, 2, painter);
    canvas.drawCircle(point2, 2, painter);
    canvas.drawCircle(point3, 2, painter);
    canvas.drawCircle(point4, 2, painter);
    painter.strokeWidth = 0.5;
    canvas.drawLine(point1, point2, painter);
    canvas.drawLine(point2, point3, painter);
    canvas.drawLine(point3, point4, painter);
    canvas.drawLine(point4, point1, painter);
    final path = Path()
      ..moveTo(point1.dx, point1.dy)
      ..lineTo(point2.dx, point2.dy)
      ..lineTo(point3.dx, point3.dy)
      ..lineTo(point4.dx, point4.dy)
      ..close();

    // Create a Paint object to represent the border
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class CurrentMousePositionPainter extends CustomPainter {
  final double scale;
  final List<double>? currentMouseCursorPosition;

  CurrentMousePositionPainter({
    this.currentMouseCursorPosition,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (currentMouseCursorPosition != null) {
      painter.color = Colors.blue;
      canvas.drawCircle(Offset(currentMouseCursorPosition!.first, currentMouseCursorPosition!.last), 6 / (scale), painter);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class RelocationPainter extends CustomPainter {
  final double scale;
  final DrawableRoot? odigoImage;
  final RelocationResponseModel? relocationPoint;

  RelocationPainter({
    this.relocationPoint,
    this.odigoImage,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    if (relocationPoint != null) {
      if (odigoImage != null) {
        painter.color = AppColors.clr009AF1;
        painter.strokeWidth = 1;
        canvas.drawLine(Offset(relocationPoint!.startPoint.x, relocationPoint!.startPoint.y), Offset(relocationPoint!.endPoint.x, relocationPoint!.endPoint.y), painter);
        final xPoint = relocationPoint!.startPoint.x;
        final yPoint = relocationPoint!.startPoint.y;
        final Offset centerPosition = Offset(MapVariables.odigoWidth / 2, MapVariables.odigoHeight / 2);
        canvas.translate(xPoint - centerPosition.dx, yPoint - centerPosition.dy);
        canvas.translate(centerPosition.dx, centerPosition.dy);
        var thetaAngle = relocationPoint!.theta * (180 / pi);
        if (thetaAngle < 0) {
          thetaAngle += 360;
        }
        canvas.rotate((thetaAngle * (pi / 180)) * -1);
        final double inverseScale = 1.0 / (scale);
        canvas.scale(inverseScale);
        canvas.translate(-centerPosition.dx, -centerPosition.dy);
        odigoImage!.draw(canvas, Rect.zero);
        canvas.scale(1 / inverseScale);
        canvas.translate(-(xPoint - centerPosition.dx), -(yPoint - centerPosition.dy));
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class ContinousDataCanvas extends CustomPainter {
  final double scale;
  final List<List<double>> laserData;
  final List<List<double>>? threeDData;
  final List<List<double>>? globalPath;

  ContinousDataCanvas({
    required this.laserData,
    this.threeDData,
    this.globalPath,
    required this.scale,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    ui.Paint painter = ui.Paint();
    for (var laser in laserData) {
      drawLaserData(canvas, painter, laser.first, laser.last);
    }
    if (threeDData != null) {
      for (var threeD in threeDData!) {
        drawThreeDData(canvas, painter, threeD.first, threeD.last);
      }
    }
    if (globalPath != null) {
      drawGlobalPath(canvas, painter, globalPath!);
    }
  }

  void drawLaserData(ui.Canvas canvas, ui.Paint painter, double dx, double dy) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    painter.color = Colors.orange;
    canvas.drawCircle(ui.Offset(xPoint, yPoint), 2 / (scale), painter);
  }

  void drawThreeDData(ui.Canvas canvas, ui.Paint painter, double dx, double dy) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    painter.color = Colors.purple;
    canvas.drawCircle(ui.Offset(xPoint, yPoint), 3 / (scale), painter);
  }

  void drawGlobalPath(ui.Canvas canvas, ui.Paint painter, List<List<double>> globalPlanList) {
    painter.color = Colors.black;
    painter.strokeWidth = 2;
    painter.style = PaintingStyle.stroke;
    var path = Path();
    if (globalPlanList.isNotEmpty) {
      path.moveTo(globalPlanList.first.first.convertXFromDasherPoint, globalPlanList.first.last.convertYFromDasherPoint);
      for (var point in globalPlanList.skip(1)) {
        path.lineTo(point.first.convertXFromDasherPoint, point.last.convertYFromDasherPoint);
      }
    }

    canvas.drawPath(path, painter);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/*class CustomPainterCanvas extends CustomPainter {
  final ui.Image image;
  final DrawableRoot? dasherImage;
  final ui.Picture? arrowImage;
  final DrawableRoot? chargingPointImage;
  final DrawableRoot? productionPointImage;
  final DrawableRoot? deliveryPointImage;
  final DrawableRoot? routeImage;
  Paint painter;
  Canvas? canvas;
  ui.Size? size;
  final double resolution;
  final double originX;
  final double originY;
  final double centerX;
  final double centerY;
  final double? scale;
  final List<Waypoint>? wayPoints;
  final List<VirtualWallPoint> virtualWalls;
  final List<VirtualWallPoint>? drawnVirtualWalls;
  final VirtualWallPoint? currentlyDrawingVirtualWall;
  final EraseVirtualWallPoint? erasedVirtualWallData;
  final RelocationResponseModel? relocationPoint;
  final RelocationResponseModel? navigationPoint;
  final Map<String, List<List<double>>>? naviRoutes;
  final List<List<double>>? currentlyDrawingRoute;
  final List<List<double>> laserData;
  final List<List<double>>? threeDData;
  final List<List<double>>? globalPath;
  final List<double>? currentMouseCursorPosition;
  final PositionEvent? currentPosition;

  CustomPainterCanvas({
    required this.image,
    this.dasherImage,
    required this.arrowImage,
    required this.chargingPointImage,
    required this.productionPointImage,
    required this.deliveryPointImage,
    this.relocationPoint,
    this.navigationPoint,
    required this.routeImage,
    required this.painter,
    required this.resolution,
    required this.originX,
    required this.originY,
    required this.centerX,
    required this.centerY,
    this.scale,
    required this.virtualWalls,
    required this.laserData,
    this.threeDData,
    this.currentPosition,
    this.drawnVirtualWalls,
    this.currentlyDrawingVirtualWall,
    this.erasedVirtualWallData,
    this.wayPoints,
    this.naviRoutes,
    this.currentlyDrawingRoute,
    this.currentMouseCursorPosition,
    this.globalPath,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    painter.color = AppColors.clrB50000;
    painter.color = AppColors.newMapColor;
    if ((scale) > 3) {
      painter.imageFilter = ui.ImageFilter.blur(sigmaX: 0.4, sigmaY: 0.4);
      painter.blendMode = BlendMode.srcOver;
    } else {
      painter = ui.Paint()..color = AppColors.newMapColor;
    }
    canvas.drawImage(image, const Offset(0, 0), painter);
    painter = ui.Paint()..color = AppColors.newMapColor;
    this.canvas = canvas;
    this.size = size;
    if (wayPoints != null) {
      wayPoints?.forEach((points) {
        drawPoints(double.parse((points.pose?.x).toString()), double.parse((points.pose?.y).toString()), points.name, pointTypeValues.map[points.type]);
      });
    }
    for (var walls in virtualWalls) {
      drawVirtualWall(walls.pose.point1.x, walls.pose.point1.y, walls.pose.point2.x, walls.pose.point2.y);
    }
    for (var laser in laserData) {
      drawLaserData(laser.first, laser.last);
    }
    if (threeDData != null) {
      for (var threeD in threeDData!) {
        drawThreeDData(threeD.first, threeD.last);
      }
    }
    if (currentlyDrawingVirtualWall != null) {
      drawStartAndEndOfVirtualWall(currentlyDrawingVirtualWall!.pose);
    }
    drawnVirtualWalls?.forEach((walls) {
      drawUpdatedVirtualWalls(walls.pose);
    });
    if (erasedVirtualWallData != null) {
      drawErasedVirtualWall(erasedVirtualWallData!.pose);
    }
    if (naviRoutes != null) {
      naviRoutes?.forEach((name, points) {
        drawNaviRoutes(name, points);
      });
    }
    if (currentlyDrawingRoute != null) {
      drawCreatingRoute();
    }
    if (currentMouseCursorPosition != null) {
      painter.color = Colors.blue;
      canvas.drawCircle(Offset(currentMouseCursorPosition!.first, currentMouseCursorPosition!.last), 6 / (scale), painter);
    }
    if (globalPath != null) {
      drawGlobalPath(globalPath!);
    }
    if (relocationPoint != null) {
      if (dasherImage != null) {
        painter.color = AppColors.clr009AF1;
        painter.strokeWidth = 1;
        canvas.drawLine(Offset(relocationPoint!.startPoint.x, relocationPoint!.startPoint.y), Offset(relocationPoint!.endPoint.x, relocationPoint!.endPoint.y), painter);
        drawRobotPosition(relocationPoint!.startPoint.x, relocationPoint!.startPoint.y, relocationPoint!.theta);
      }
    }
    if (navigationPoint != null) {
      if (dasherImage != null) {
        painter.color = AppColors.clr009AF1;
        painter.strokeWidth = 1;
        canvas.drawLine(Offset(navigationPoint!.startPoint.x, navigationPoint!.startPoint.y), Offset(navigationPoint!.endPoint.x, navigationPoint!.endPoint.y), painter);
        drawRobotPosition(navigationPoint!.startPoint.x, navigationPoint!.startPoint.y, navigationPoint!.theta);
      }
    }
    if (dasherImage != null && currentPosition != null && relocationPoint == null && navigationPoint == null) {
      drawRobotPosition(currentPosition!.x!.convertXFromDasherPoint, currentPosition!.y!.convertYFromDasherPoint, currentPosition!.theta!);
    }
  }

  void drawPoints(double dx, double dy, String name, PointType? type) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    Color textColor = Colors.black;
    double desiredWidth = (24.0 / ((1.2) - 0.5));
    double desiredHeight = (24.0 / ((1.2) - 0.5));
    if ((scale) > 1.2) {
      desiredWidth = (24.0 / ((scale) - 0.5));
      desiredHeight = (24.0 / ((scale) - 0.5));
    }
    canvas?.translate(xPoint - (desiredWidth / 2), yPoint - desiredHeight);
    switch (type) {
      case PointType.PRODUCTION:
        textColor = const ui.Color(0xFFAFB42C);
        // Calculate scaling factors
        final scaleX = desiredWidth / productionPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / productionPointImage!.viewport.viewBoxRect.height;
        canvas?.scale(scaleX, scaleY);
        productionPointImage!.draw(canvas!, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas?.scale((1 / scaleX), (1 / scaleY));
        break;
      case PointType.CHARGE:
        textColor = const ui.Color(0xFF03950C);
        final scaleX = desiredWidth / chargingPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / chargingPointImage!.viewport.viewBoxRect.height;
        canvas?.scale(scaleX, scaleY);
        chargingPointImage!.draw(canvas!, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas?.scale((1 / scaleX), (1 / scaleY));
        break;

      case PointType.ROUTE:
        textColor = const ui.Color(0xFFBB7F4D);
        final scaleX = desiredWidth / routeImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / routeImage!.viewport.viewBoxRect.height;
        canvas?.scale(scaleX, scaleY);
        routeImage!.draw(canvas!, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas?.scale((1 / scaleX), (1 / scaleY));
        break;
      default:
        textColor = const ui.Color(0xFF126EEf);
        final scaleX = desiredWidth / deliveryPointImage!.viewport.viewBoxRect.width;
        final scaleY = desiredHeight / deliveryPointImage!.viewport.viewBoxRect.height;
        canvas?.scale(scaleX, scaleY);
        deliveryPointImage!.draw(canvas!, Rect.fromPoints(Offset(xPoint, yPoint), Offset.zero));
        canvas?.scale((1 / scaleX), (1 / scaleY));
        break;
    }
    canvas?.translate((xPoint - (desiredWidth / 2)) * -1, (yPoint - (desiredHeight)) * -1);
    final ui.ParagraphBuilder paragraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(fontSize: (11 / (scale)), fontWeight: FontWeight.w800))
      ..pushStyle(ui.TextStyle(color: textColor))
      ..addText(name);
    final ui.Paragraph paragraph = paragraphBuilder.build()..layout(ui.ParagraphConstraints(width: (size?.width ?? 0)));
    canvas?.drawParagraph(paragraph, Offset(xPoint - (paragraph.longestLine / 2), yPoint + (5 / (scale)) / 2));
  }

  void drawNaviRoutes(String name, List<List<double>> route) {
    painter.color = const ui.Color(0xFFBB7F4D);
    for (var routePath in route) {
      canvas?.drawCircle(Offset(routePath.first.convertXFromDasherPoint, routePath.last.convertYFromDasherPoint), 2 / (scale), painter);
    }
    int routePathIndex = 0;
    for (routePathIndex = 0; routePathIndex < route.length; routePathIndex++) {
      double x1Point = route[routePathIndex].first.convertXFromDasherPoint;
      double y1Point = route[routePathIndex].last.convertYFromDasherPoint;
      double x2Point = 0;
      double y2Point = 0;
      if (routePathIndex == (route.length - 1)) {
        x2Point = route[0].first.convertXFromDasherPoint;
        y2Point = route[0].last.convertYFromDasherPoint;
      } else {
        x2Point = route[routePathIndex + 1].first.convertXFromDasherPoint;
        y2Point = route[routePathIndex + 1].last.convertYFromDasherPoint;
        if (routePathIndex == 0) {
          drawPoints(route[routePathIndex].first, route[routePathIndex].last, name, PointType.ROUTE);
        }
        Offset end = ui.Offset(x2Point, y2Point);
        Offset start = ui.Offset(x1Point, y1Point);
        double totalDistance = (end - start).distance;
        double dx = (end.dx - start.dx) / totalDistance;
        double dy = (end.dy - start.dy) / totalDistance;

        double currentDistance = 0;
        double dashLength = 4;
        double dashGap = 2;

        while (currentDistance < totalDistance) {
          final double startX = start.dx + dx * currentDistance;
          final double startY = start.dy + dy * currentDistance;

          currentDistance += dashLength;

          if (currentDistance > totalDistance) {
            currentDistance = totalDistance;
          }

          final double endX = start.dx + dx * currentDistance;
          final double endY = start.dy + dy * currentDistance;

          canvas?.drawLine(Offset(startX, startY), Offset(endX, endY), painter);

          currentDistance += dashGap;
        }
      }
    }
  }

  void drawCreatingRoute() {
    painter.color = const ui.Color(0xFF2FB9F8);
    if (currentlyDrawingRoute?.isNotEmpty ?? false) {
      canvas?.drawLine(Offset(currentlyDrawingRoute!.last.first, currentlyDrawingRoute!.last.last), Offset(currentPosition!.x!.convertXFromDasherPoint, currentPosition!.y!.convertYFromDasherPoint), painter);
    }
    painter.color = const ui.Color(0xFF2FB9F8);
    for (var routePath in currentlyDrawingRoute!) {
      canvas?.drawCircle(Offset(routePath.first, routePath.last), 4 / (scale), painter);
    }
    int routePathIndex = 0;
    for (routePathIndex = 0; routePathIndex < currentlyDrawingRoute!.length; routePathIndex++) {
      double x1Point = currentlyDrawingRoute![routePathIndex].first;
      double y1Point = currentlyDrawingRoute![routePathIndex].last;
      double x2Point = 0;
      double y2Point = 0;
      if (routePathIndex != (currentlyDrawingRoute!.length - 1)) {
        x2Point = currentlyDrawingRoute![routePathIndex + 1].first;
        y2Point = currentlyDrawingRoute![routePathIndex + 1].last;
        Offset end = ui.Offset(x2Point, y2Point);
        Offset start = ui.Offset(x1Point, y1Point);
        canvas?.drawLine(start, end, painter);
      }
    }
  }

  void drawVirtualWall(double dx1, double dy1, double dx2, double dy2) {
    final x1Point = dx1.convertXFromDasherPoint;
    final y1Point = dy1.convertYFromDasherPoint;
    final x2Point = dx2.convertXFromDasherPoint;
    final y2Point = dy2.convertYFromDasherPoint;
    painter.color = Colors.red;
    painter.strokeWidth = 2;
    canvas?.drawLine(ui.Offset(x1Point, y1Point), ui.Offset(x2Point, y2Point), painter);
  }

  void drawStartAndEndOfVirtualWall(VirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x;
    final wallPoint1Y = virtualWallPose.point1.y;
    final wallPoint2X = virtualWallPose.point2.x;
    final wallPoint2Y = virtualWallPose.point2.y;
    painter.color = Colors.blue;
    painter.strokeWidth = 1;
    canvas?.drawLine(ui.Offset(wallPoint1X, wallPoint1Y), ui.Offset(wallPoint2X, wallPoint2Y), painter);
  }

  void drawErasedVirtualWall(EraseVirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x;
    final wallPoint1Y = virtualWallPose.point1.y;
    final wallPoint2X = virtualWallPose.point2.x;
    final wallPoint2Y = virtualWallPose.point2.y;
    final wallPoint3X = virtualWallPose.point3.x;
    final wallPoint3Y = virtualWallPose.point3.y;
    final wallPoint4X = virtualWallPose.point4.x;
    final wallPoint4Y = virtualWallPose.point4.y;
    ui.Offset point1 = ui.Offset(wallPoint1X, wallPoint1Y);
    ui.Offset point2 = ui.Offset(wallPoint2X, wallPoint2Y);
    ui.Offset point3 = ui.Offset(wallPoint3X, wallPoint3Y);
    ui.Offset point4 = ui.Offset(wallPoint4X, wallPoint4Y);
    painter.color = Colors.blue;
    canvas?.drawCircle(point1, 2, painter);
    canvas?.drawCircle(point2, 2, painter);
    canvas?.drawCircle(point3, 2, painter);
    canvas?.drawCircle(point4, 2, painter);
    painter.strokeWidth = 0.5;
    canvas?.drawLine(point1, point2, painter);
    canvas?.drawLine(point2, point3, painter);
    canvas?.drawLine(point3, point4, painter);
    canvas?.drawLine(point4, point1, painter);
    final path = Path()
      ..moveTo(point1.dx, point1.dy)
      ..lineTo(point2.dx, point2.dy)
      ..lineTo(point3.dx, point3.dy)
      ..lineTo(point4.dx, point4.dy)
      ..close();

    // Create a Paint object to represent the border
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    canvas?.drawPath(path, paint);
  }

  void drawUpdatedVirtualWalls(VirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x.convertXFromDasherPoint;
    final wallPoint1Y = virtualWallPose.point1.y.convertYFromDasherPoint;
    final wallPoint2X = virtualWallPose.point2.x.convertXFromDasherPoint;
    final wallPoint2Y = virtualWallPose.point2.y.convertYFromDasherPoint;
    painter.strokeWidth = 1;
    painter.color = Colors.green;
    canvas?.drawLine(ui.Offset(wallPoint1X, wallPoint1Y), ui.Offset(wallPoint2X, wallPoint2Y), painter);
    painter.color = Colors.blue;
  }

  void drawLaserData(double dx, double dy) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    painter.color = Colors.orange;
    canvas?.drawCircle(ui.Offset(xPoint, yPoint), 2 / (scale), painter);
  }

  void drawThreeDData(double dx, double dy) {
    final xPoint = dx.convertXFromDasherPoint;
    final yPoint = dy.convertYFromDasherPoint;
    painter.color = Colors.purple;
    canvas?.drawCircle(ui.Offset(xPoint, yPoint), 3 / (scale), painter);
  }



  void drawSpecialArea(Polygon polygon) {
    var specialAreaIndex = 0;
    double x1Point = polygon.polygon[specialAreaIndex].first.convertXFromDasherPoint;
    double y1Point = polygon.polygon[specialAreaIndex].last.convertYFromDasherPoint;
    final path = Path()..moveTo(x1Point, y1Point);
    while (specialAreaIndex < polygon.polygon.length) {
      x1Point = polygon.polygon[specialAreaIndex].first.convertXFromDasherPoint;
      y1Point = polygon.polygon[specialAreaIndex].last.convertYFromDasherPoint;
      double x2Point = 0;
      double y2Point = 0;
      if (specialAreaIndex == (polygon.polygon.length - 1)) {
        x2Point = polygon.polygon[0].first.convertXFromDasherPoint;
        y2Point = polygon.polygon[0].last.convertYFromDasherPoint;
      } else {
        x2Point = polygon.polygon[specialAreaIndex + 1].first.convertXFromDasherPoint;
        y2Point = polygon.polygon[specialAreaIndex + 1].last.convertYFromDasherPoint;
        path.lineTo(x2Point, y2Point);
        Offset end = ui.Offset(x2Point, y2Point);
        Offset start = ui.Offset(x1Point, y1Point);
        double totalDistance = (end - start).distance;
        double dx = (end.dx - start.dx) / totalDistance;
        double dy = (end.dy - start.dy) / totalDistance;

        double currentDistance = 0;
        double dashLength = 4;
        double dashGap = 1;

        while (currentDistance < totalDistance) {
          final double startX = start.dx + dx * currentDistance;
          final double startY = start.dy + dy * currentDistance;

          currentDistance += dashLength;

          if (currentDistance > totalDistance) {
            currentDistance = totalDistance;
          }

          final double endX = start.dx + dx * currentDistance;
          final double endY = start.dy + dy * currentDistance;
          painter.color = Colors.purple;
          painter.strokeWidth = 1.5;
          canvas?.drawLine(Offset(startX, startY), Offset(endX, endY), painter);

          currentDistance += dashGap;
        }
      }
      specialAreaIndex++;
    }
    final paint = Paint()
      ..color = Colors.purple.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    canvas?.drawPath(path, paint);
    x1Point = polygon.polygon[0].first.convertXFromDasherPoint;
    y1Point = polygon.polygon[0].last.convertYFromDasherPoint;
    drawToolTipWithInfo(message: polygon.name, toolTipPosition: Offset(x1Point, y1Point), color: Colors.purple, textColor: AppColors.white);
  }

  void drawGlobalPath(List<List<double>> globalPlanList) {
    painter.color = Colors.black;
    painter.strokeWidth = 2;
    painter.style = PaintingStyle.stroke;
    var path = Path();
    if (globalPlanList.isNotEmpty) {
      path.moveTo(globalPlanList.first.first.convertXFromDasherPoint, globalPlanList.first.last.convertYFromDasherPoint);
      for (var point in globalPlanList.skip(1)) {
        path.lineTo(point.first.convertXFromDasherPoint, point.last.convertYFromDasherPoint);
      }
    }

    canvas?.drawPath(path, painter);
  }


  void drawUpdatedVirtualWalls(VirtualWallPose virtualWallPose) {
    final wallPoint1X = virtualWallPose.point1.x.convertXFromDasherPoint;
    final wallPoint1Y = virtualWallPose.point1.y.convertYFromDasherPoint;
    final wallPoint2X = virtualWallPose.point2.x.convertXFromDasherPoint;
    final wallPoint2Y = virtualWallPose.point2.y.convertYFromDasherPoint;
    painter.strokeWidth = 1;
    painter.color = Colors.green;
    canvas?.drawLine(ui.Offset(wallPoint1X, wallPoint1Y), ui.Offset(wallPoint2X, wallPoint2Y), painter);
    painter.color = Colors.blue;
  }

  void drawRobotPosition(double dx, double dy, double theta) async {
    final xPoint = dx;
    final yPoint = dy;
    final Offset centerPosition = Offset(MapVariables.odigoWidth / 2, MapVariables.odigoHeight / 2);
    canvas?.translate(xPoint - centerPosition.dx, yPoint - centerPosition.dy);
    canvas?.translate(centerPosition.dx, centerPosition.dy);
    var thetaAngle = theta * (180 / pi);
    if (thetaAngle < 0) {
      thetaAngle += 360;
    }
    canvas?.rotate((thetaAngle * (pi / 180)) * -1);
    final double inverseScale = 1.0 / (scale);
    canvas?.scale(inverseScale);
    canvas?.translate(-centerPosition.dx, -centerPosition.dy);
    dasherImage!.draw(canvas!, Rect.zero);
    canvas?.scale(1 / inverseScale);
    canvas?.translate(-(xPoint - centerPosition.dx), -(yPoint - centerPosition.dy));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}*/
