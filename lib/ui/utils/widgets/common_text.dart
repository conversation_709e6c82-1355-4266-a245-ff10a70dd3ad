import 'package:odigo_offline/ui/utils/theme/theme.dart';

class CommonText extends StatelessWidget {
  final String title;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final double? fontSize;
  final Color? clrfont;
  final TextOverflow? textOverflow;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextDecoration? textDecoration;
  final TextStyle? textStyle;
  final bool? softWrap;

  const CommonText({
    super.key,
    this.title = '',
    this.fontWeight,
    this.fontStyle,
    this.fontSize,
    this.clrfont,
    this.maxLines,
    this.textAlign,
    this.textDecoration,
    this.textStyle,
    this.textOverflow,
    this.softWrap,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      textScaleFactor: 1.0,
      maxLines: maxLines,
      textAlign: textAlign,
      softWrap: softWrap,
      overflow: textOverflow ?? TextOverflow.ellipsis,
      style: textStyle ?? TextStyles.regular.copyWith(fontFamily: TextStyles.fontFamily, fontWeight: fontWeight ?? TextStyles.fwRegular, fontSize: fontSize ?? 40.sp, color: clrfont ?? AppColors.black, fontStyle: fontStyle ?? FontStyle.normal, decoration: textDecoration ?? TextDecoration.none),
    );
  }
}
