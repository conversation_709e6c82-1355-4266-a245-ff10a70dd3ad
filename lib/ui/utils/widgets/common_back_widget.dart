import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';

class CommonBackWidget extends ConsumerWidget with BaseConsumerWidget {
  final Color? color;
  final Function()? onTap;

  const CommonBackWidget({super.key, this.color, this.onTap});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    return ref.watch(navigationStackController).items.length > 1
        ? ListBounceAnimation(
            onTap: () {
              if (onTap != null) {
                onTap?.call();
              } else {
                if (ref.read(navigationStackController).items.length > 1) {
                  ref.read(navigationStackController).pop();
                }
              }
            },
            child: CommonSVG(
              strIcon: Assets.svgs.svgCrossRounded.path,
              height: context.height * 0.03,
            ),
          )
        : const Offstage();
  }
}
