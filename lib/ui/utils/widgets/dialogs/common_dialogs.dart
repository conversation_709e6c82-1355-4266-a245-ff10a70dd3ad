import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/progress_controller/progress_controller.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_offline/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/form_validations.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_form_field.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/confirmation_dialog.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

/// Confirmation dialog  message
showConfirmationDialog(BuildContext context, String title,
    {String? message, String? positiveButtonText, String? negativeButtonText, int? mainTimerSeconds, required Function(bool isPositive) didTakeAction, bool isAutoPop = false}) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    barrierColor: AppColors.bg1A2D3170,
    builder: (context) {
      return ConfirmationDialog(
        title: title,
        message: message,
        positiveButtonText: positiveButtonText,
        negativeButtonText: negativeButtonText,
        didTakeAction: didTakeAction,
        mainTimerSeconds: mainTimerSeconds,
        isAutoPop: isAutoPop,
      );
    },
  );
}

/// Message Dialog
showMessageDialog(BuildContext context, {required String message, Function()? didDismiss, bool isError = false}) {
  return showDialog(
      barrierDismissible: false,
      context: context,
      barrierColor: AppColors.bg1A2D3170,
      builder: (context) => Dialog(
            backgroundColor: AppColors.white,
            insetPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40.r)),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                SizedBox(
                  width: MediaQuery.sizeOf(context).width * 0.7,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 30),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CommonSVG(
                          strIcon: isError ? Assets.svgs.svgWarning.path : Assets.svgs.svgInfo.path,
                          height: context.height * 0.07,
                        ),
                        SizedBox(height: context.height * 0.02),
                        CommonText(
                          title: message,
                          textAlign: TextAlign.center,
                          maxLines: 5,
                          textStyle: TextStyles.medium.copyWith(fontSize: 40.sp, color: AppColors.black),
                        ),
                        SizedBox(height: context.height * 0.02),
                        CommonButton(
                          height: context.height * 0.05,
                          buttonText: LocaleKeys.keyOk.localized,
                          backgroundColor: AppColors.primary,
                          borderColor: AppColors.black,
                          buttonTextColor: AppColors.black010101,
                          onTap: () {
                            Navigator.pop(context);
                            if (didDismiss != null) {
                              Future.delayed(const Duration(milliseconds: 80), () {
                                didDismiss();
                              });
                            }
                          },
                          borderWidth: 1.0,
                          buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 30.sp),
                        ).paddingSymmetric(horizontal: 20.0, vertical: 11.0)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ));
}

/// Widget Dialog
showWidgetDialog(BuildContext context, Widget widget, {Color? bgColor, bool? givePadding, Function(AnimationController animationController)? onPopCall}) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    barrierColor: AppColors.bg1A2D3170,
    builder: (context) => FadeBoxTransition(
      onPopCall: onPopCall,
      delay: 200,
      child: Dialog(
        backgroundColor: bgColor ?? AppColors.clr212121,
        insetPadding: (givePadding ?? true) ? EdgeInsets.all(60.sp) : EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50.r)),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Wrap(
              children: [
                widget,
              ],
            ).paddingSymmetric(horizontal: 30.w, vertical: 30.h),
          ],
        ),
      ),
    ),
  );
}

showCommonImageVideoDialog({required BuildContext context, required Widget dialogBody, double? width, bool? barrierDismissible, double? height, GlobalKey? keyBadge}) {
  return showDialog(
    context: context,
    barrierDismissible: barrierDismissible ?? false,
    builder: (context) {
      return FadeBoxTransition(
        child: ShowDialogMobile(height: height, width: width, dialogBody: dialogBody, key_: keyBadge!),
      );
    },
  );
}

class ShowDialogMobile extends StatelessWidget with BaseStatelessWidget {
  final double? height;
  final double? width;
  final Widget dialogBody;
  final Key key_;

  const ShowDialogMobile({super.key, this.height, this.width, required this.dialogBody, required this.key_});

  @override
  Widget buildPage(BuildContext context) {
    return Dialog(
      key: key_,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(20.r),
        ),
        width: (context.width * (width ?? 1)),
        height: height != null ? (context.height * (height ?? 1)) : null,
        child: dialogBody,
      ),
    );
  }
}

showCommonSwitchingDialog({required BuildContext context, String? text, double? width, double? height, GlobalKey? key, bool showProgress = false, bool isBarrierDismissible = false, Widget? textWidget, Function? onDialogPop}) {
  return showDialog(
    context: context,
    barrierDismissible: isBarrierDismissible,
    builder: (context) {
      globalRef?.read(progressController).progressText = text ?? LocaleKeys.keySwitchingToMapBuilding.localized;
      return Dialog(
        key: key,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50.r)),
        child: ListBounceAnimation(
          onTap: () {
            hideKeyboard();
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50.r),
              color: AppColors.white,
            ),
            height: height ?? context.height * 0.2,
            width: width ?? context.width * 0.6,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Consumer(
                  builder: (BuildContext context, WidgetRef ref, Widget? child) {
                    final progressWatch = ref.watch(progressController);
                    return showProgress && (progressWatch.progress != 0 && progressWatch.progress != 100)
                        ? SizedBox(
                            height: context.height * 0.15,
                            width: context.width * 0.15,
                            child: SfRadialGauge(
                              axes: <RadialAxis>[
                                RadialAxis(
                                  minimum: 0,
                                  maximum: 100,
                                  showLabels: false,
                                  pointers: <GaugePointer>[
                                    RangePointer(
                                      value: progressWatch.progress,
                                      cornerStyle: CornerStyle.bothCurve,
                                      width: 0.2,
                                      sizeUnit: GaugeSizeUnit.factor,
                                      color: AppColors.black,
                                    )
                                  ],
                                  showTicks: false,
                                  axisLineStyle: AxisLineStyle(
                                    thickness: 0.2,
                                    cornerStyle: CornerStyle.bothCurve,
                                    color: AppColors.black.withOpacity(0.2),
                                    thicknessUnit: GaugeSizeUnit.factor,
                                  ),
                                  annotations: <GaugeAnnotation>[
                                    GaugeAnnotation(
                                      positionFactor: 0.1,
                                      angle: 90,
                                      widget: Text(
                                        '${progressWatch.progress.toStringAsFixed(2)}%',
                                        style: TextStyles.bold.copyWith(fontSize: 16.sp, color: AppColors.black),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          )
                        : const CircularProgressIndicator(color: AppColors.black).paddingSymmetric(vertical: 20.h);
                  },
                ),
                (textWidget ??
                        Consumer(
                          builder: (context, ref, child) {
                            final progressWatch = ref.watch(progressController);
                            return CommonText(
                              title: progressWatch.progressText,
                              maxLines: 3,
                              textAlign: TextAlign.center,
                              textStyle: TextStyles.regular.copyWith(
                                fontSize: 28.h,
                                color: AppColors.clr7A7A7A,
                              ),
                            );
                          },
                        ))
                    .paddingSymmetric(horizontal: context.width * 0.05),
              ],
            ),
          ),
        ),
      );
    },
  ).then((value) => onDialogPop?.call());
}

showMarkPointDialog(
  BuildContext mainContext, {
  required String strIcon,
  required String title,
  required String? pointType,
  required PositionEvent? position,
  bool showNameDialog = true,
  Function? onConfirmClicked,
}) {
  showDialog(
    context: mainContext,
    builder: (context) {
      return Dialog(
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40.r)),
        child: SizedBox(
          height: context.height * 0.55,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.width * 0.04, vertical: context.height * 0.01),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: ListBounceAnimation(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: CommonSVG(strIcon: Assets.svgs.svgCrossRounded.path),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.width * 0.05),
                  child: Column(
                    children: [
                      CommonSVG(strIcon: strIcon),
                      SizedBox(height: context.height * 0.01),
                      CommonText(
                        title: title,
                        textAlign: TextAlign.center,
                        textStyle: TextStyles.semiBold.copyWith(fontSize: 22.sp, color: AppColors.black),
                        maxLines: 4,
                      ),
                      SizedBox(height: context.height * 0.03),
                      CommonButton(
                        height: context.height * 0.05,
                        backgroundColor: AppColors.clr009AF1,
                        buttonText: LocaleKeys.keyConfirm.localized.toUpperCase(),
                        borderRadius: BorderRadius.circular(60.r),
                        buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.white),
                        onTap: () {
                          Navigator.pop(context);
                          onConfirmClicked?.call();
                          if (showNameDialog) {
                            showMarkPointTextDialog(mainContext, pointType: pointType, position: position);
                          }
                        },
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}

showMarkPointTextDialog(BuildContext context, {String? title, String? pointType, PositionEvent? position, Function(String)? onTapValidate}) {
  TextEditingController textEditingController = TextEditingController();
  GlobalKey<FormState> key = GlobalKey();
  showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40.r)),
        child: SizedBox(
          height: context.height * 0.3,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.width * 0.04, vertical: context.height * 0.01),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: CommonText(
                        title: title ?? 'Point Name',
                        textStyle: TextStyles.bold.copyWith(fontSize: 32.sp, color: AppColors.black),
                      ),
                    ),
                    ListBounceAnimation(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: CommonSVG(strIcon: Assets.svgs.svgCrossRounded.path),
                    ),
                  ],
                ),
                SizedBox(height: context.height * 0.02),
                if (position != null)
                  CommonText(
                    title: 'X:${position.x} | Y:${position.y} | θ:${position.theta}',
                    textStyle: TextStyles.bold.copyWith(fontSize: 32.sp, color: AppColors.black),
                  ),
                SizedBox(height: context.height * 0.02),
                Form(
                  key: key,
                  child: CommonInputFormField(
                    textEditingController: textEditingController,
                    validator: (value) => validateText(value, 'Point name cannot be empty'),
                    hintText: 'Enter point name here',
                    hintTextStyle: TextStyles.regular.copyWith(fontSize: 26.sp, color: AppColors.black.withOpacity(0.4)),
                    fieldTextStyle: TextStyles.regular.copyWith(fontSize: 26.sp, color: AppColors.black.withOpacity(0.4)),
                    borderRadius: BorderRadius.circular(60.r),
                    backgroundColor: AppColors.clrECECEC,
                    borderColor: AppColors.transparent,
                  ),
                ),
                SizedBox(height: context.height * 0.02),
                CommonButton(
                  height: context.height * 0.05,
                  backgroundColor: AppColors.clr009AF1,
                  buttonText: LocaleKeys.keySave.localized.toUpperCase(),
                  borderRadius: BorderRadius.circular(60.r),
                  buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.white),
                  onTap: () {
                    RobotEventManger robotEventManager = RobotEventManger.instance;
                    if ((key.currentState?.validate() ?? false)) {
                      if (position != null && pointType != null) {
                        robotEventManager.setPoint(
                          x: position.x ?? 0,
                          y: position.y ?? 0,
                          theta: position.theta ?? 0,
                          pointName: textEditingController.text,
                          pointType: pointType,
                        );
                      }
                      onTapValidate?.call(textEditingController.text);
                      Navigator.pop(context);
                    }
                  },
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}

showEditPointTextWithDeleteDialog(BuildContext context, {String? title, required Waypoint point, Function(String)? onTapValidate, Function? onDeleteTap}) {
  TextEditingController textEditingController = TextEditingController(text: point.name);
  GlobalKey<FormState> key = GlobalKey();
  showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40.r)),
        child: SizedBox(
          height: context.height * 0.3,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: context.width * 0.04, vertical: context.height * 0.01),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: CommonText(
                        title: title ?? 'Point Name',
                        textStyle: TextStyles.bold.copyWith(fontSize: 32.sp, color: AppColors.black),
                      ),
                    ),
                    ListBounceAnimation(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: CommonSVG(strIcon: Assets.svgs.svgCrossRounded.path),
                    ),
                  ],
                ),
                SizedBox(height: context.height * 0.02),
                CommonText(
                  title: 'X:${point.pose?.x} | Y:${point.pose?.y} | θ:${point.pose?.theta}',
                  textStyle: TextStyles.bold.copyWith(fontSize: 32.sp, color: AppColors.black),
                ),
                SizedBox(height: context.height * 0.02),
                Form(
                  key: key,
                  child: CommonInputFormField(
                    textEditingController: textEditingController,
                    validator: (value) => validateText(value, 'Point name cannot be empty'),
                    hintText: 'Enter point name here',
                    hintTextStyle: TextStyles.regular.copyWith(fontSize: 26.sp, color: AppColors.black.withOpacity(0.4)),
                    fieldTextStyle: TextStyles.regular.copyWith(fontSize: 26.sp, color: AppColors.black.withOpacity(0.4)),
                    borderRadius: BorderRadius.circular(60.r),
                    backgroundColor: AppColors.clrECECEC,
                    borderColor: AppColors.transparent,
                  ),
                ),
                SizedBox(height: context.height * 0.02),
                Row(
                  children: [
                    Expanded(
                      child: CommonButton(
                        height: context.height * 0.05,
                        backgroundColor: AppColors.clr009AF1,
                        buttonText: 'DELETE THIS POINT',
                        borderRadius: BorderRadius.circular(60.r),
                        buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.white),
                        onTap: () {
                          onDeleteTap?.call();
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    SizedBox(width: context.width * 0.02),
                    Expanded(
                      child: CommonButton(
                        height: context.height * 0.05,
                        backgroundColor: AppColors.clr009AF1,
                        buttonText: LocaleKeys.keyEdit.localized.toUpperCase(),
                        borderRadius: BorderRadius.circular(60.r),
                        buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.white),
                        onTap: () {
                          if ((key.currentState?.validate() ?? false)) {
                            onTapValidate?.call(textEditingController.text);
                            Navigator.pop(context);
                          }
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}
