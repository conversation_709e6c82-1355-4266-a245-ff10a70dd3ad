import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const Color primary = Color(0xff0F0F0F);
  static const Color whiteF7F7FC = Color(0xffF7F7FC);
  static const Color grey3A3A3A = Color(0xff3A3A3A);
  static const Color clr212121 = Color(0xff212121);
  static const Color grey707070 = Color(0xff707070);
  static const Color greyF7F7F7 = Color(0xffF7F7F7);
  static const Color black272727 = Color(0xff272727);
  static const Color grey8D8C8C = Color(0xff8D8C8C);
  static const Color black171717 = Color(0xff171717);
  static const Color clr7A7A7A = Color(0xFF7A7A7A);
  static const Color clrF2F7FF = Color(0xffF2F7FF);

  static const Color mapColor = Color(0xff88CCCF);
  // static const Color newMapColor = Color(0xffFFB289);
  static const Color newMapColor = Color(0xff84cfff);
  static const Color clrB50000 = Color(0xffB50000);

  static const Color white = Color(0xffffffff);
  static const Color black = Color(0xff000000);
  static const Color darkGrey = Color(0xff010101);
  static const Color primaryColor = Color(0xff181818);
  static const Color primaryColorLight = Color(0xff282828);
  static const Color darkRed = Color(0xFFD9251C);
  static const Color black010101 = Color(0xff010101);
  static const Color black292929 = Color(0xff292929);
  static const Color transparent = Color(0x00000000);
  static const Color black070707 = Color(0xff070707);
  static const Color greyDEDEDE = Color(0xffDEDEDE);
  static const Color bg1A2D3170 = Color(0x1A2D3170);
  static const Color fontLight = Color(0xff8B8B8B);
  static const Color grey2B2B2B = Color(0xff2B2B2B);
  static const Color clrE5E5E5 = Color(0xffE5E5E5);
  static const Color clrECECEC = Color(0xffE5E5E5);
  static const Color clr545454 = Color(0xff545454);
  static const Color clr009AF1 = Color(0xff009AF1);
  static const Color clrE34850 = Color(0xffE34850);
  static const Color clr60A80D = Color(0xff60A80D);
  static const Color textFieldBorderColor = Color(0xFF8D8D8D);
  static const Color clr8D8D8D = Color(0xff8D8D8D);
  static const Color errorColor = Color(0xffFF5757);
  static const Color clrD5D5D5 = Color(0xffD5D5D5);
  static const Color clr313339 = Color(0xff313339);
  static const Color clr3A3A3A = Color(0xff3A3A3A);

  static Color get textMainFontByTheme => white;

  static Color get scaffoldBGByTheme => primary;
}
