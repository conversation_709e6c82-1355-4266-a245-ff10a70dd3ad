import 'package:odigo_offline/ui/utils/theme/theme.dart';

class ThemeStyle {
  static ThemeData themeData(bool isDarkTheme, BuildContext context) {
    return ThemeData(
      fontFamily: TextStyles.fontFamily,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.scaffoldBGByTheme,
      textTheme: Theme.of(context).textTheme.apply(bodyColor: AppColors.textMainFontByTheme),
      // splashColor: AppColors.transparent,
      // //To disable ripple effect

      // highlightColor: AppColors.transparent,
      // //To disable ripple effect

      appBarTheme: AppBarTheme(
        elevation: 0.0,
        backgroundColor: AppColors.scaffoldBGByTheme,
      ),
    );
  }
}
