enum ScreenName { settings, none }

enum NetworkStateEnum {
  CONNECTING,
  CONNECTED,
  SUSPENDED,
  DISCONNECTING,
  DISCONNECTED,
  UNKNOWN,
}

final networkStateValues = EnumValues<NetworkStateEnum>({
  'CONNECTING': NetworkStateEnum.CONNECTING,
  'CONNECTED': NetworkStateEnum.CONNECTED,
  'SUSPENDED': NetworkStateEnum.SUSPENDED,
  'DISCONNECTING': NetworkStateEnum.DISCONNECTING,
  'DISCONNECTED': NetworkStateEnum.DISCONNECTED,
  'UNKNOWN': NetworkStateEnum.UNKNOWN,
});

enum DetailedState {
  /// Ready to start data connection setup.
  IDLE,

  /// Searching for an available access point.
  SCANNING,

  /// Currently setting up data connection.
  CONNECTING,

  /// Network link established, performing authentication.
  AUTHENTICATING,

  /// Awaiting response from DHCP server in order to assign IP address information.
  OBTAINING_IPADDR,

  /// IP traffic should be available.
  CONNECTED,

  /// IP traffic is suspended
  SUSPENDED,

  /// Currently tearing down data connection.
  DISCONNECTING,

  /// IP traffic not available.
  DISCONNECTED,

  /// Attempt to connect failed.
  FAILED,

  /// Access to this network is blocked.
  BLOCKED,

  /// Link has poor connectivity.
  VERIFYING_POOR_LINK,

  /// Checking if network is a captive portal
  CAPTIVE_PORTAL_CHECK
}

final class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map) {
    reverse;
  }

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}

class ReverseEnumValues<T> {
  Map<T, String> map;

  ReverseEnumValues(this.map);
}

enum DisplayMode { CRUISE, TOUR, NAVIGATION }

final displayModeValues = EnumValues({
  "CRUISE": DisplayMode.CRUISE,
  "TOUR": DisplayMode.TOUR,
  "NAVIGATION": DisplayMode.NAVIGATION,
});

enum AssetType {
  video,
  image,
  audio,
}

final assetTypeValues = EnumValues({
  "AUDIO": AssetType.audio,
  "IMAGE": AssetType.image,
  "VIDEO": AssetType.video,
});

enum DefaultSettingType { general, onWay, reached }

enum SoundType { obstacle, emergency }

enum LocationPointType { delivery, charge, production }

enum PlayingMediaType { none, video, audio }

enum ReceivedNavigationEventType { none, started, completed, error }

enum PointTypeEnum {
  delivery,
  charge,
  production,
  recycle,
  avoid,
}

final pointEnumValues = EnumValues<PointTypeEnum>({
  '1': PointTypeEnum.charge,
  '2': PointTypeEnum.delivery,
  '3': PointTypeEnum.production,
  '4': PointTypeEnum.recycle,
});

final pointEnumValuesString = ReverseEnumValues<PointTypeEnum>({
  PointTypeEnum.charge: 'ChargingPoint',
  PointTypeEnum.delivery: '',
  PointTypeEnum.production: 'ProductionPoint',
  PointTypeEnum.recycle: 'RecyclingPoint',
});

enum MapModeEnum {
  BUILD_MODE_CHANGED,
  LOCATION_POINT_ADDED,
  LOCATION_POINT_EXISTS,
  LOCATION_POINT_FAILED,
}

final mapEnumValues = EnumValues<MapModeEnum>({
  MapModeEnum.BUILD_MODE_CHANGED.name: MapModeEnum.BUILD_MODE_CHANGED,
  MapModeEnum.LOCATION_POINT_ADDED.name: MapModeEnum.LOCATION_POINT_ADDED,
  MapModeEnum.LOCATION_POINT_EXISTS.name: MapModeEnum.LOCATION_POINT_EXISTS,
  MapModeEnum.LOCATION_POINT_FAILED.name: MapModeEnum.LOCATION_POINT_FAILED,
});

enum PointType { CHARGE, DELIVERY, PRODUCTION, ROUTE, ALL, ELEVATOR, STORE }

final pointTypeValues = EnumValues({
  'charge': PointType.CHARGE,
  'delivery': PointType.DELIVERY,
  'production': PointType.PRODUCTION,
  'all': PointType.ALL,
});

enum ViewType { collapsed, expanded }
