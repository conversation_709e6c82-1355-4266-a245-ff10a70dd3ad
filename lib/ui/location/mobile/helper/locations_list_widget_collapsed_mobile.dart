import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/controller/locations/locations_controller.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';

import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_offline/ui/utils/anim/show_down_transition.dart';
import 'package:odigo_offline/ui/utils/anim/slide_left_transition.dart';
import 'package:odigo_offline/ui/utils/anim/slide_right_transition.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:odigo_offline/ui/utils/widgets/empty_state_widget.dart';

class LocationListWidgetCollapsedMobile extends ConsumerWidget with BaseConsumerWidget {

  const LocationListWidgetCollapsedMobile({super.key});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final locationsWatch = ref.watch(locationsController);
    return FadeBoxTransition(
      child: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: EdgeInsets.zero,
              itemCount: locationsWatch.locationsList.length,
              itemBuilder: (context, index) {
                LocationModel? locations = locationsWatch.locationsList[index];
                return ListBounceAnimation(
                  onTap: () {
                    ref.read(navigationStackController).push(NavigationStackItem.locationDetails(locationId: locations.id));
                  },
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(50.r)),
                      color: AppColors.grey3A3A3A,
                    ),
                    padding: EdgeInsets.symmetric(vertical: context.height * 0.005),
                    child: Row(
                      children: [
                        CommonSVG(
                          strIcon: locations.locationPoint.target?.type == LocationPointType.production.name
                              ? Assets.svgs.svgOdigoProdcutionPoint.path
                              : locations.locationPoint.target?.type == LocationPointType.charge.name
                                  ? Assets.svgs.svgMarkChargingPoint.path
                                  : Assets.svgs.svgOdigoLocationPoint.path,
                          width: context.width * 0.05,
                        ),
                        SizedBox(width: context.width * 0.05),
                        Expanded(
                          child: Row(
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    CommonText(
                                      title: locations.locationName ?? '',
                                      textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp),
                                    ),
                                    Container(
                                      width: 0.1,
                                      height: context.height * 0.03,
                                      color: AppColors.white,
                                      margin: EdgeInsets.symmetric(horizontal: context.width * 0.03),
                                    ),
                                    CommonText(
                                      title: locations.locationPoint.target?.name ?? '',
                                      textStyle: TextStyles.regular.copyWith(color: AppColors.white.withOpacity(0.8), fontSize: 28.sp),
                                    ),
                                  ],
                                ),
                              ),
                              ListBounceAnimation(
                                onTap: () {
                                  showConfirmationDialog(
                                    context,
                                    'Remove',
                                    message: 'Are you sure to remove this location?\nDeleting location will result in deletion of tours associated with this location.',
                                    didTakeAction: (isPositive) async {
                                      if (isPositive) {
                                        await locationsWatch.deleteLocation(locationUuid: locations.locationUuid);
                                        ref.read(helpInfoDashboardController).getLocationPointList(type: LocationPointType.delivery);
                                      }
                                    },
                                  );
                                },
                                child: Icon(Icons.delete_forever, color: AppColors.clrE34850, size: context.height * 0.025),
                              ),
                              Container(
                                width: 0.1,
                                height: context.height * 0.03,
                                color: AppColors.white,
                                margin: EdgeInsets.symmetric(horizontal: context.width * 0.03),
                              ),
                              ListBounceAnimation(
                                onTap: () async {
                                  ref.read(navigationStackController).push(NavigationStackItem.locationDetails(locationId: locations.id));
                                },
                                child: Icon(Icons.keyboard_arrow_right, color: AppColors.white, size: context.height * 0.03),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ).paddingSymmetric(horizontal: context.width * 0.03),
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return SizedBox(height: context.height * 0.01);
              },
            ),
          ),
        ],
      ),
    );
  }
}
