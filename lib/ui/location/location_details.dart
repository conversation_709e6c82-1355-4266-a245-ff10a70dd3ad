import 'package:flutter/material.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:odigo_offline/ui/location/mobile/location_details_mobile.dart';

class LocationDetails extends StatelessWidget with BaseStatelessWidget {
  final int? locationId;
  final ScreenName? screenName;
  final LocationPointType? pointType;
  const LocationDetails({Key? key, this.locationId,this.pointType,this.screenName}) : super(key: key);

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    return ScreenTypeLayout.builder(
        mobile: (BuildContext context) {
          return LocationDetailsMobile(locationId: locationId,screenName:screenName,pointType:pointType);
        },
    );
  }
}

