import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';


class CommonPermissionTile extends StatelessWidget {
  const CommonPermissionTile({super.key, required this.onButtonTap, required this.permissionTitle, required this.permissionDescription, required this.permissionIcon});

  final void Function() onButtonTap;
  final String permissionTitle;
  final String permissionDescription;
  final String permissionIcon;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(width: context.width * 0.05),
        CommonSVG(
          strIcon: permissionIcon,
          height: context.height * 0.08,
        ),
        Sized<PERSON>ox(width: context.width * 0.05),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonText(
              title: permissionTitle,
              textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
            ),
            SizedBox(height: context.height * 0.005),
            CommonText(
              title: permissionDescription,
              textStyle: TextStyles.regular.copyWith(fontSize: 30.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
            ),
          ],
        ),
        const Spacer(),
        CommonButton(
          height: context.height * 0.035,
          width: context.width * 0.2,
          buttonText: LocaleKeys.keyAllow.localized,
          onTap: onButtonTap,
        )
      ],
    );
  }
}
