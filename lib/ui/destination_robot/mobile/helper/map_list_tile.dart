import 'dart:async';
import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_offline/framework/controller/map/map_controller.dart';
import 'package:odigo_offline/framework/repository/ip_map/model/map_list_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/destination_robot/mobile/helper/alias_dialog_widget.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/text_style.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:image/image.dart' as IMG;
import 'dart:ui' as ui;

class MapListTile extends ConsumerStatefulWidget {
  final IpMapElement mapData;
  final String? buttonText;
  final Function? onApplyTap;

  const MapListTile({
    super.key,
    required this.mapData,
    this.buttonText,
    this.onApplyTap,
  });

  @override
  ConsumerState<MapListTile> createState() => _MapListTileState();
}

class _MapListTileState extends ConsumerState<MapListTile> with BaseConsumerStatefulWidget {
  ui.Image? image;

  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      try {
        var img = base64Decode(widget.mapData.map!.imageUrl!.replaceAll('data:image/png;base64,', ''));
        IMG.decodeImage(img);
        final Completer<ui.Image> completer = Completer();
        ui.decodeImageFromList(img, (ui.Image img) {
          return completer.complete(img);
        });
        image = await MapVariables.map.changeColor(await completer.future);
      } on Exception {
        image = null;
      } finally {
        if (context.mounted) setState(() {});
      }
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final mapWatch = ref.watch(mapController);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30.r),
        color: AppColors.clr212121,
      ),
      padding: EdgeInsets.symmetric(horizontal: context.width * 0.01, vertical: context.height * 0.005),
      child: Row(
        children: [
          Builder(
            builder: (context) {
              if (image == null) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(30.r),
                  child: CommonSVG(
                    strIcon: Assets.svgs.svgMapPlaceholder.path,
                    height: context.width * 0.15,
                    width: context.width * 0.15,
                  ),
                );
              } else {
                return ListBounceAnimation(
                  onTap: () {
                    showWidgetDialog(
                      context,
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(30.r),
                            child: RawImage(
                              image: image,
                              fit: BoxFit.cover,
                              height: context.width,
                              width: context.width,
                            ),
                          ),
                          SizedBox(height: context.height * 0.02),
                          CommonButton(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            width: context.width * 0.2,
                            height: context.height * 0.05,
                            buttonText: 'Done',
                          )
                        ],
                      ),
                    );
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(30.r),
                    child: RawImage(
                      image: image,
                      fit: BoxFit.cover,
                      height: context.width * 0.15,
                      width: context.width * 0.15,
                    ),
                  ),
                );
              }
            },
          ),
          SizedBox(width: context.width * 0.02),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonText(
                  title: widget.mapData.name ?? '',
                  textStyle: TextStyles.regular.copyWith(fontSize: 26.sp),
                ),
                CommonText(
                  title: widget.mapData.alias ?? '',
                  textStyle: TextStyles.regular.copyWith(fontSize: 22.sp, color: AppColors.white.withOpacity(0.6)),
                ),
              ],
            ),
          ),
          SizedBox(width: context.width * 0.01),
          Row(
            children: [
              SizedBox(width: context.width * 0.01),
              ListBounceAnimation(
                onTap: () async {
                  widget.onApplyTap?.call();
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: context.height * 0.01),
                  child: Icon(Icons.select_all_sharp, color: AppColors.clr009AF1, size: context.height * 0.025),
                ),
              ),
              SizedBox(width: context.width * 0.02),
              ListBounceAnimation(
                onTap: () async {
                  showDialog(
                    context: context,
                    useSafeArea: false,
                    builder: (context) => AliasDialogWidget(
                      mapData: widget.mapData,
                      onTap: () async {
                        showCommonSwitchingDialog(context: context, text: 'Naming Alias');
                        await CanvasMapApiClass.api.renameMap(widget.mapData.name ?? '', mapWatch.aliasNameController.text);
                        if (mounted) await mapWatch.ipMapListDataApi(this.context);
                        if (mounted) Navigator.pop(this.context);
                        if (context.mounted) Navigator.pop(context);
                      },
                    ),
                  );
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: context.height * 0.01),
                  child: CommonSVG(
                    strIcon: Assets.svgs.svgEditMap.path,
                    height: context.height * 0.02,
                  ),
                ),
              ),
              if (mapWatch.currentMap?.name != widget.mapData.name) ...{
                SizedBox(width: context.width * 0.02),
                ListBounceAnimation(
                  onTap: () async {
                    showConfirmationDialog(
                      context,
                      'Delete Map',
                      message: 'Are you sure to delete this map',
                      didTakeAction: (isPositive) async {
                        if (isPositive) {
                          if (widget.mapData.name != null) {
                            showCommonSwitchingDialog(context: this.context, text: 'Deleting Map');
                            await CanvasMapApiClass.api.deleteMap(widget.mapData.name!);
                            if (mounted) await mapWatch.ipMapListDataApi(this.context);
                            if (mounted) Navigator.pop(this.context);
                          }
                        }
                      },
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: context.height * 0.01),
                    child: CommonSVG(
                      strIcon: Assets.svgs.svgDeleteMap.path,
                      height: context.height * 0.02,
                    ),
                  ),
                ),
              },
              SizedBox(width: context.width * 0.01),
            ],
          ),
        ],
      ),
    );
  }
}
