import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/map/map_controller.dart';
import 'package:odigo_offline/framework/repository/ip_map/model/map_list_response_model.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/ui/new_dashboard/custom_keyboard/artistic_multilingual_keyboard.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/common_input_form_field.dart' as tf;

class AliasDialogWidget extends ConsumerStatefulWidget {
  final IpMapElement? mapData;
  final Function onTap;

  const AliasDialogWidget({super.key, this.mapData, required this.onTap});

  @override
  ConsumerState<AliasDialogWidget> createState() => _AliasDialogWidgetState();
}

class _AliasDialogWidgetState extends ConsumerState<AliasDialogWidget> with BaseConsumerStatefulWidget {
  FocusNode focusNode = FocusNode();

  @override
  Widget buildPage(BuildContext context) {
    final mapWatch = ref.watch(mapController);
    focusNode.requestFocus();
    if (widget.mapData != null) {
      mapWatch.aliasNameController.text = widget.mapData!.alias ?? '';
    }
    return Dialog(
      insetPadding: EdgeInsets.zero,
      clipBehavior: Clip.none,
      child: Container(
        height: (globalContext?.height ?? 0) * 0.45,
        width: context.width * 0.95,
        padding: EdgeInsets.symmetric(horizontal: context.width * 0.03, vertical: context.height * 0.015),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: CommonText(
                    title: 'Set Map Alias',
                    textStyle: TextStyles.bold.copyWith(color: AppColors.black),
                  ),
                ),
                CommonBackWidget(
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            SizedBox(height: context.height * 0.02),
            tf.CommonInputFormField(
              textEditingController: mapWatch.aliasNameController,
              borderRadius: BorderRadius.circular(50.r),
              hintText: 'Alias',
              focusNode: focusNode,
              hintTextStyle: TextStyles.medium.copyWith(color: AppColors.black.withOpacity(0.4)),
              fieldTextStyle: TextStyles.medium.copyWith(color: AppColors.black),
              readOnly: true,
              cursorColor: AppColors.black,
              validator: (val) {
                if (val?.isNotEmpty ?? false) {
                  return null;
                }
                return 'Alias is Required';
              },
            ),
            SizedBox(height: context.height * 0.01),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(23.r),
                color: AppColors.white.withOpacity(0.4),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(23.r),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: sigma, sigmaY: sigma),
                  child: Container(
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(23.r), color: AppColors.black.withOpacity(0.4)),
                    height: context.height * 0.2,
                    width: context.width * 0.9,
                    padding: EdgeInsets.symmetric(
                      horizontal: context.width * 0.01,
                      vertical: context.height * 0.02,
                    ),
                    alignment: Alignment.center,
                    child: KeyboardLayouts(
                      showGlassKeyboard: true,
                      textEditingController: mapWatch.aliasNameController,
                      focusNode: FocusNode(),
                      enableLanguageButton: false,
                      keyShadowColor: Colors.black,
                      keyboardBackgroundColor: AppColors.transparent,
                      keysBackgroundColor: AppColors.black.withOpacity(0.3),
                      keyTextStyle: TextStyle(color: AppColors.white, fontSize: 22.sp),
                      keyBorderRadius: BorderRadius.circular(12.r),
                      showDoneButton: false,
                      currentKeyboardLanguage: KeyboardLanguages.english,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: context.height * 0.01),
            CommonButton(
              height: context.height * 0.05,
              width: context.width * 0.3,
              buttonText: 'Save',
              backgroundColor: AppColors.clr009AF1,
              onTap: () {
                widget.onTap.call();
              },
            ),
          ],
        ),
      ),
    );
  }
}
