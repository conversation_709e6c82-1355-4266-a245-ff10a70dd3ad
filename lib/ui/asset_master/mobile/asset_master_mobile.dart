import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg_custom/flutter_svg.dart';
import 'package:odigo_offline/framework/controller/asset_master/asset_master_controller.dart';
import 'package:odigo_offline/framework/controller/default_settings/default_settings_controller.dart';
import 'package:odigo_offline/framework/controller/nearby_manager/nearby_controller.dart';
import 'package:odigo_offline/framework/controller/splash/splash_controller.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/extension.dart';
import 'package:odigo_offline/ui/asset_master/mobile/helper/collasped_asset_list_widget.dart';

import 'package:odigo_offline/ui/asset_master/mobile/helper/expanded_asset_list_widget.dart';
import 'package:odigo_offline/ui/asset_master/mobile/helper/show_nearby_qr_dialog.dart';
import 'package:odigo_offline/ui/new_dashboard/helper/controlbar_slider.dart';

import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/anim/custom_size_transition.dart';
import 'package:odigo_offline/ui/utils/anim/slide_left_transition.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/common_button.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';
import 'package:odigo_offline/ui/utils/widgets/common_switch_view_widget.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:odigo_offline/ui/utils/widgets/empty_state_widget.dart';

class AssetMasterMobile extends ConsumerStatefulWidget {
  const AssetMasterMobile({super.key});

  @override
  ConsumerState<AssetMasterMobile> createState() => _AssetMasterMobileState();
}

class _AssetMasterMobileState extends ConsumerState<AssetMasterMobile> with BaseConsumerStatefulWidget {
  DrawableRoot? volume;

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      handleStartAdvertising();
      final assetMasterWatch = ref.read(assetMasterController);
      assetMasterWatch.getAssetList();
      volume = await MapVariables.map.loadSvgRoot(Assets.svgs.svgVolume.path);
      assetMasterWatch.showQrRow = true;
      assetMasterWatch.notifyListeners();
      assetMasterWatch.assetScrollController.addListener(scrollHandler);
      Future.delayed(Durations.medium3, () {
        assetMasterWatch.animateToBottom();
      });
    });
  }

  Future<void> handleStartAdvertising() async {
    final nearbyManagementWatch = ref.read(nearbyManagementController);
    if (context.mounted) {
      await nearbyManagementWatch.startAdvertising(context);
    }
  }

  @override
  void dispose() {
    globalRef?.read(nearbyManagementController).nearbyManager.stopAllEndpoints();
    globalRef?.read(nearbyManagementController).nearbyManager.stopAdvertising();
    globalRef?.read(nearbyManagementController).showQrButton = true;
    globalRef?.read(assetMasterController).assetScrollController.removeListener(scrollHandler);
    super.dispose();
  }

  void scrollHandler() {
    final assetMasterWatch = ref.read(assetMasterController);
    double currentPosition = assetMasterWatch.assetScrollController.offset;
    if (mounted) {
      if (currentPosition >= (assetMasterWatch.assetScrollController.position.maxScrollExtent - 50)) {
        if ((!assetMasterWatch.showQrRow)) assetMasterWatch.updateShowQrRow(true);
      } else if (assetMasterWatch.showQrRow) {
        assetMasterWatch.updateShowQrRow(false);
      }
    }
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    final assetMasterWatch = ref.watch(assetMasterController);
    final nearbyManagementWatch = ref.watch(nearbyManagementController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Stack(
        children: [
          AbsorbPointer(
            absorbing: !nearbyManagementWatch.allowInteraction,
            child: SizedBox(
              height: context.height,
              child: assetMasterWatch.assetListFromSelectedTab.isNotEmpty
                  ? SingleChildScrollView(
                      controller: assetMasterWatch.assetScrollController,
                      child: Column(
                        children: [
                          SizedBox(height: context.height * 0.15),
                          if (assetMasterWatch.selectedViewType == ViewType.expanded) ...{
                            const ExpandedAssetListWidget(),
                          } else ...{
                            const CollapsedAssetListWidget(),
                          },
                          SizedBox(height: context.height * 0.6 + ((volume != null && assetMasterWatch.selectedAssetType != AssetType.image) ? context.height * 0.06 : 0)),
                        ],
                      ),
                    )
                  : const EmptyStateWidget(),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              color: AppColors.black.withOpacity(0.1),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonText(
                        title: 'Media List',
                        textStyle: TextStyles.medium.copyWith(
                          fontSize: 50.sp,
                          fontFamily: TextStyles.manropeFontFamily,
                        ),
                      ),
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: context.width * 0.05),
                          width: context.width,
                          decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(50.r)),
                          padding: EdgeInsets.symmetric(vertical: context.height * 0.01, horizontal: context.width * 0.02),
                          child: CommonText(
                            title: 'Discoverable as: ${RobotMetaDataRepository.instance.odigoName ?? RobotMetaDataRepository.instance.androidId}',
                            textStyle: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 30.sp),
                          ),
                        ),
                      ),
                      CommonBackWidget(
                        onTap: () {
                          ref.read(navigationStackController).pop();
                        },
                        color: AppColors.white,
                      ),
                    ],
                  ).paddingSymmetric(horizontal: screenPadding.horizontal, vertical: screenPadding.vertical),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        height: context.height * 0.05,
                        width: context.width * 0.7,
                        decoration: BoxDecoration(
                          color: AppColors.clr212121,
                          borderRadius: BorderRadius.circular(32.r),
                        ),
                        child: Row(
                          children: [
                            ...List.generate(
                              assetMasterWatch.assetTypeList.length,
                              (index) {
                                return Expanded(
                                  child: ListBounceAnimation(
                                    onTap: () {
                                      if (assetMasterWatch.selectedAssetType != assetMasterWatch.assetTypeList[index]) {
                                        assetMasterWatch.updateAssetType(assetMasterWatch.assetTypeList[index]);
                                        Future.delayed(Durations.medium2, () {
                                          assetMasterWatch.jumpToBottom();
                                        });
                                      }
                                    },
                                    child: Container(
                                      height: context.height,
                                      padding: EdgeInsets.symmetric(horizontal: context.width * 0.02),
                                      alignment: Alignment.center,
                                      decoration: (assetMasterWatch.assetTypeList[index] == assetMasterWatch.selectedAssetType)
                                          ? BoxDecoration(
                                              border: Border.all(color: AppColors.white, width: 2.h),
                                              borderRadius: BorderRadius.circular(32.r),
                                            )
                                          : null,
                                      child: CommonText(
                                        title:
                                            '${(assetMasterWatch.assetTypeList[index].name).toUpperCase()}${assetMasterWatch.selectedAssetType == assetMasterWatch.assetTypeList[index] ? ' (${assetMasterWatch.assetListFromSelectedTab.length})' : ''}',
                                        maxLines: 2,
                                        textStyle: TextStyles.medium.copyWith(
                                          fontSize: 36.sp,
                                          fontFamily: TextStyles.manropeFontFamily,
                                          color: (assetMasterWatch.assetTypeList[index] == assetMasterWatch.selectedAssetType) ? AppColors.white : AppColors.clr545454,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      CommonSwitchViewWidget(
                        selectedViewType: assetMasterWatch.selectedViewType,
                        onViewTypeChanged: (viewType) {
                          assetMasterWatch.updateSelectedViewType(viewType);
                        },
                      ),
                    ],
                  ).paddingSymmetric(horizontal: screenPadding.horizontal),
                  SizedBox(height: context.height * 0.02),
                ],
              ),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              color: AppColors.black.withOpacity(0.1),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: context.height * 0.01),
                  if (assetMasterWatch.showQrRow) ...{
                    if (volume != null && assetMasterWatch.selectedAssetType != AssetType.image)
                      Consumer(
                        builder: (context, ref, child) {
                          final defaultSettingWatch = ref.watch(defaultSettingsController);
                          return ControlBarSlider(
                            value: defaultSettingWatch.volume,
                            min: 0,
                            max: 1,
                            percentageValue: (defaultSettingWatch.volume * 100).toInt(),
                            bottomIcon: volume,
                            onChanged: (volume) {
                              defaultSettingWatch.updateVolume(volume: volume);
                            },
                          );
                        },
                      ).paddingSymmetric(horizontal: screenPadding.horizontal),
                    SizedBox(height: context.height * 0.02),
                    CustomSizeTransition(
                      onAnimationCreated: (animationController) => assetMasterWatch.showQrRowAnimationController = animationController,
                      child: Row(
                        children: [
                          Expanded(
                            child: nearbyManagementWatch.showQrButton
                                ? CommonButton(
                                    buttonText: "Show QR Code",
                                    showLoader: nearbyManagementWatch.isShowQrButtonLoading,
                                    onTap: () async {
                                      nearbyManagementWatch.updateIsShowQrButtonLoading(true);
                                      nearbyManagementWatch.encryptWifiQrData(context);
                                      if (context.mounted) {
                                        showNearbyQrDialog(context: context, ref: ref);
                                      }
                                      nearbyManagementWatch.updateIsShowQrButtonLoading(false);
                                    },
                                  )
                                : const Offstage(),
                          ),
                          SizedBox(width: context.width * 0.02),
                          ListBounceAnimation(
                            onTap: () {
                              showConfirmationDialog(
                                context,
                                'Delete All ${assetMasterWatch.selectedAssetType.name.toLowerCase().capitalizeFirstLetterOfSentence}s',
                                message: 'Are you sure you want to delete all ${assetMasterWatch.selectedAssetType.name.toLowerCase().capitalizeFirstLetterOfSentence}s from assets, this action cannot be undone',
                                positiveButtonText: 'Delete All',
                                negativeButtonText: 'Cancel',
                                didTakeAction: (isPositive) async {
                                  if (isPositive) {
                                    assetMasterWatch.deleteAssetFromAssetType(assetMasterWatch.selectedAssetType);
                                    if (!nearbyManagementWatch.showQrButton && nearbyManagementWatch.connectedRemoteId != null) {
                                      nearbyManagementWatch.nearbyManager.sendBytesPayload(nearbyManagementWatch.connectedRemoteId!, await assetMasterWatch.allSendToRemoteData);
                                    }
                                  }
                                },
                              );
                            },
                            child: Container(
                              decoration: const BoxDecoration(shape: BoxShape.circle, color: AppColors.darkRed),
                              padding: EdgeInsets.symmetric(horizontal: context.width * 0.02, vertical: context.height * 0.01),
                              child: CommonSVG(
                                strIcon: Assets.svgs.svgDeleteMap.path,
                                svgColor: AppColors.white,
                                height: context.height * 0.025,
                              ),
                            ),
                          )
                        ],
                      ),
                    ).paddingSymmetric(horizontal: screenPadding.horizontal),
                  },
                  if (!assetMasterWatch.showQrRow)
                    Row(
                      children: [
                        Expanded(
                          child: volume != null && assetMasterWatch.selectedAssetType != AssetType.image
                              ? Consumer(
                                  builder: (context, ref, child) {
                                    final defaultSettingWatch = ref.watch(defaultSettingsController);
                                    return ControlBarSlider(
                                      value: defaultSettingWatch.volume,
                                      min: 0,
                                      max: 1,
                                      percentageValue: (defaultSettingWatch.volume * 100).toInt(),
                                      bottomIcon: volume,
                                      onChanged: (volume) {
                                        defaultSettingWatch.updateVolume(volume: volume);
                                      },
                                    );
                                  },
                                ).paddingSymmetric(horizontal: screenPadding.horizontal)
                              : const Offstage(),
                        ),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: SlideLeftTransition(
                            onAnimationCreated: (animationController) => assetMasterWatch.showScrollDownIconAnimationController = animationController,
                            child: ListBounceAnimation(
                              onTap: assetMasterWatch.animateToBottom,
                              child: CommonSVG(
                                strIcon: Assets.svgs.svgScrollToBottom.path,
                                height: context.height * 0.05,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                ],
              ).paddingOnly(right: context.width * 0.05, bottom: context.height * 0.03, top: context.height * 0.03),
            ),
          ),
        ],
      ),
    );
  }

  EdgeInsets get screenPadding {
    return EdgeInsets.symmetric(vertical: context.height * 0.01, horizontal: context.width * 0.015);
  }
}
