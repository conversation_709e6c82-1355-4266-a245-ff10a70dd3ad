import 'dart:io';

import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/framework/utils/extension/context_extension.dart';
import 'package:odigo_offline/framework/utils/extension/double_extension.dart';
import 'package:odigo_offline/ui/asset_master/mobile/helper/audio_player_dialog.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/helper/base_widget.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/common_svg.dart';

class CommonAssetTile extends ConsumerStatefulWidget {
  const CommonAssetTile(
    this.asset, {
    super.key,
    this.onDeleteTap,
    this.onSelectionTap,
    this.isSelected = false,
    this.isMultiSelection = false,
    required this.onPreviewTap,
  });

  final Asset asset;
  final Function onPreviewTap;
  final Function? onDeleteTap;
  final Function()? onSelectionTap;
  final bool isSelected;
  final bool isMultiSelection;

  @override
  ConsumerState<CommonAssetTile> createState() => _CommonAssetTileState();
}

class _CommonAssetTileState extends ConsumerState<CommonAssetTile> with AutomaticKeepAliveClientMixin, BaseConsumerStatefulWidget {
  String? path;
  bool keepAlive = true;

  ///Init
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {});
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  ///Build
  @override
  Widget buildPage(BuildContext context) {
    super.build(context);
    final isAudio = widget.asset.assetType?.toLowerCase() == AssetType.audio.name;
    if (isAudio) {
      path = widget.asset.assetPath;
    } else {
      path = widget.asset.assetThumbnail?.decryptedFilePath;
    }
    final assetFile = File(path ?? '');
    return Stack(
      children: [
        SizedBox(
          height: context.height * 0.2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ListBounceAnimation(
                  onTap: () {
                    if (widget.asset.assetType?.toUpperCase() != AssetType.audio.name.toUpperCase()) {
                      widget.onPreviewTap.call();
                    } else {
                      showDialog(context: context, builder: (context) => AudioPlayerDialog(asset: widget.asset));
                    }
                  },
                  child: ClipRRect(
                    child: widget.asset.assetType?.toUpperCase() == AssetType.audio.name.toUpperCase()
                        ? CommonSVG(
                            strIcon: Assets.svgs.svgAudioIcon.path,
                            height: context.height * 0.1,
                          )
                        : Stack(
                            children: [
                              if (assetFile.existsSync())
                                Image.file(
                                  assetFile,
                                  fit: BoxFit.cover,
                                  width: context.width,
                                  height: context.height,
                                  gaplessPlayback: true,
                                )
                              else
                                Container(
                                  height: context.height * 0.2,
                                  padding: EdgeInsets.symmetric(horizontal: context.width * 0.05),
                                  child: CommonSVG(
                                    strIcon: Assets.svgs.svgContentNotAvailable.path,
                                  ),
                                ),
                              if (widget.onDeleteTap != null)
                                Positioned(
                                  bottom: context.width * 0.01,
                                  right: context.width * 0.01,
                                  child: ListBounceAnimation(
                                    onTap: () {
                                      widget.onDeleteTap?.call();
                                    },
                                    child: Container(
                                      decoration: const BoxDecoration(shape: BoxShape.circle, color: AppColors.darkRed),
                                      padding: EdgeInsets.symmetric(horizontal: context.width * 0.02, vertical: context.height * 0.01),
                                      child: CommonSVG(
                                        strIcon: Assets.svgs.svgDeleteMap.path,
                                        svgColor: AppColors.white,
                                        height: context.height * 0.015,
                                      ),
                                    ),
                                  ),
                                )
                            ],
                          ),
                  ),
                ),
              ),
              if (widget.asset.assetType?.toLowerCase() == AssetType.audio.name) ...{
                SizedBox(height: context.width * 0.005),
                CommonText(
                  title: '${widget.asset.assetName}',
                  textStyle: TextStyles.semiBold.copyWith(fontSize: 22.sp, color: AppColors.white),
                ),
                SizedBox(height: context.width * 0.005),
                CommonText(
                  title: (double.tryParse('${assetFile.statSync().size}') ?? 0.0).getFileSizeString,
                  textStyle: TextStyles.regular.copyWith(fontSize: 22.sp, color: AppColors.white),
                ),
                SizedBox(height: context.width * 0.005),
                CommonText(
                  title: widget.asset.duration?.toString().formatTimeHHMMSS ?? '00:00',
                  textStyle: TextStyles.regular.copyWith(fontSize: 22.sp, color: AppColors.white),
                ),
                SizedBox(height: context.width * 0.005),
                CommonText(
                  title: (double.tryParse('${assetFile.statSync().size}') ?? 0.0).getFileSizeString,
                  textStyle: TextStyles.regular.copyWith(fontSize: 22.sp, color: AppColors.white),
                ),
                SizedBox(height: context.width * 0.005),
                ListBounceAnimation(
                  onTap: () {
                    widget.onDeleteTap?.call();
                  },
                  child: CommonText(
                    title: 'Delete',
                    textStyle: TextStyles.regular.copyWith(fontSize: 22.sp, color: AppColors.clrE34850),
                  ),
                ),
              }
            ],
          ),
        ),
        if (widget.onSelectionTap != null)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              color: AppColors.black.withOpacity(0.2),
              alignment: Alignment.bottomRight,
              padding: EdgeInsets.symmetric(vertical: context.height * 0.005, horizontal: context.width * 0.01),
              child: ListBounceAnimation(
                onTap: widget.onSelectionTap!,
                child: CommonSVG(
                  strIcon: widget.isMultiSelection
                      ? widget.isSelected
                          ? Assets.svgs.svgFilledCheckbox.keyName
                          : Assets.svgs.svgEmptyCheckbox.keyName
                      : widget.isSelected
                          ? Assets.svgs.svgSelectedRoute.keyName
                          : Assets.svgs.svgRadioUnselected.keyName,
                  height: 50.h,
                  width: 50.h,
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => widget.onDeleteTap == null;
}
