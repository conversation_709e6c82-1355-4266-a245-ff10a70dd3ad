import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';

part 'navigation_stack_item.freezed.dart';

@freezed
class NavigationStackItem with _$NavigationStackItem {
  const factory NavigationStackItem.splash() = NavigationStackItemSplashPage;
  const factory NavigationStackItem.newDashboard() = NavigationStackItemNewDashboardPage;
  const factory NavigationStackItem.mapList({bool? fromOperator}) = NavigationStackItemMapList;
  const factory NavigationStackItem.displayAds() = NavigationStackItemViewDisplayAdsPage;
  const factory NavigationStackItem.availableWiFi({bool? dpPop}) = NavigationStackItemAvailableWiFi;
  const factory NavigationStackItem.aboutDevice() = NavigationStackItemAboutDevice;
  const factory NavigationStackItem.createTour({int? tourIndex}) = NavigationStackItemCreateTour;
  const factory NavigationStackItem.tourList() = NavigationStackItemTourList;
  const factory NavigationStackItem.defaultSettings() = NavigationStackItemDefaultSettings;
  const factory NavigationStackItem.locations({ScreenName? screenName,LocationPointType? pointType}) = NavigationStackItemLocations;
  const factory NavigationStackItem.locationDetails({int? locationId,ScreenName? screenName,LocationPointType? pointType}) = NavigationStackItemLocationDetails;
  const factory NavigationStackItem.assetMaster() = NavigationStackItemAssetMaster;
  const factory NavigationStackItem.routeList() = NavigationStackItemRouteListPage;
  const factory NavigationStackItem.assetPreview({required List<Asset> assetList, required int index, Function(Asset)? onDeleteTap}) = NavigationStackItemAssetPreview;
  const factory NavigationStackItem.relocate() = NavigationStackItemRelocate;
  const factory NavigationStackItem.manageMap({bool? isPop, bool? isNewMap}) = NavigationStackItemManageMap;
  const factory NavigationStackItem.routeAsset() = NavigationStackItemRouteAsset;
  const factory NavigationStackItem.robotSettings() = NavigationStackItemRobotSettings;
  const factory NavigationStackItem.dashboardMedia() = NavigationStackItemDashboardMedia;
}