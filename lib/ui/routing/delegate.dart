import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import 'package:odigo_offline/ui/about_device/about_device.dart';
import 'package:odigo_offline/ui/asset_master/asset_master.dart';
import 'package:odigo_offline/ui/asset_preview/asset_preview.dart';
import 'package:odigo_offline/ui/canvas_map/manage_map.dart';
import 'package:odigo_offline/ui/dashboard_media/dashboard_media.dart';
import 'package:odigo_offline/ui/default_settings/default_settings.dart';
import 'package:odigo_offline/ui/destination_robot/destination_robot_list.dart';
import 'package:odigo_offline/ui/display_ads/display_ads_screen.dart';
import 'package:odigo_offline/ui/location/location_details.dart';
import 'package:odigo_offline/ui/location/locations.dart';
import 'package:odigo_offline/ui/new_dashboard/mobile/new_dashboard_mobile.dart';
import 'package:odigo_offline/ui/relocate/relocate.dart';
import 'package:odigo_offline/ui/robot_settings/robot_settings.dart';
import 'package:odigo_offline/ui/route/route_asset.dart';
import 'package:odigo_offline/ui/route/route_list.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_keys.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/splash/splash.dart';
import 'package:odigo_offline/ui/tour/create_tour.dart';
import 'package:odigo_offline/ui/tour/tour_list.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/wifi/available_wifi.dart';

final globalNavigatorKey = GlobalKey<NavigatorState>();

@injectable
class MainRouterDelegate extends RouterDelegate<NavigationStack> with ChangeNotifier, PopNavigatorRouterDelegateMixin {
  final NavigationStack stack;

  @override
  void dispose() {
    stack.removeListener(notifyListeners);
    super.dispose();
  }

  MainRouterDelegate(@factoryParam this.stack) : super() {
    stack.addListener(notifyListeners);
  }

  @override
  final navigatorKey = globalNavigatorKey;

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return Navigator(
        key: navigatorKey,
        pages: _pages(ref),

        /// callback when a page is popped.
        onPopPage: (route, result) {
          /// let the OS handle the back press if there was nothing to pop
          if (!route.didPop(result)) {
            return false;
          }

          /// if we are on root, let OS close app
          if (stack.items.length == 1) return false;

          /// if we are on root, let OS close app
          if (stack.items.isEmpty) return false;

          /// otherwise, pop the stack and consume the event
          stack.pop();
          return true;
        },
      );
    });
  }

  List<Page> _pages(WidgetRef ref) => stack.items
      .mapIndexed((e, i) => e.when(
            splash: () => const MaterialPage(child: Splash(), key: ValueKey(Keys.splash)),
            newDashboard: () => const MaterialPage(child: NewDashboardMobile(), key: ValueKey(Keys.newDashboard)),
            displayAds: () => const MaterialPage(child: DisplayAdsScreen(), key: ValueKey(Keys.displayAds)),
            availableWiFi: (doPop) => MaterialPage(child: AvailableWifi(dpPop: doPop ?? false), key: const ValueKey(Keys.availableWiFi)),
            aboutDevice: () => const MaterialPage(child: AboutDevice(), key: ValueKey(Keys.aboutDevice)),
            createTour: (tourIndex) => MaterialPage(child: CreateTourScreen(tourIndex: tourIndex), key: const ValueKey(Keys.createTour)),
            tourList: () => const MaterialPage(child: TourListScreen(), key: ValueKey(Keys.tourList)),
            defaultSettings: () => const MaterialPage(child: DefaultSettings(), key: ValueKey(Keys.defaultSettings)),
            locations: (screenName, pointType) => MaterialPage(child: Locations(screenName: screenName, pointType: pointType), key: const ValueKey(Keys.locations)),
            locationDetails: (locationId, screenName, pointType) => MaterialPage(child: LocationDetails(locationId: locationId, screenName: screenName, pointType: pointType), key: const ValueKey(Keys.locationDetails)),
            assetMaster: () => const MaterialPage(child: AssetMaster(), key: ValueKey(Keys.assetMaster)),
            routeList: () => const MaterialPage(child: RouteList(), key: ValueKey(Keys.routeList)),
            assetPreview: (assetList, index, onDeleteTap) => MaterialPage(child: AssetPreview(assetList: assetList, index: index, onDeleteTap: onDeleteTap), key: const ValueKey(Keys.assetPreview)),
            relocate: () => const MaterialPage(child: Relocate(), key: ValueKey(Keys.relocate)),
            routeAsset: () => const MaterialPage(child: RouteAssets(), key: ValueKey(Keys.routeAsset)),
            mapList: (fromOperator) => MaterialPage(child: DestinationRobotList(fromOperator: fromOperator ?? false), key: const ValueKey(Keys.mapList)),
            manageMap: (isPop, isNewMap) => MaterialPage(child: ManageMap(isPop: isPop, isNewMap: isNewMap ?? false), key: const ValueKey(Keys.manageMap)),
            robotSettings: () => const MaterialPage(child: RobotSettings(), key: ValueKey(Keys.robotSettings)),
            dashboardMedia: () => const MaterialPage(child: DashboardMedia(), key: ValueKey(Keys.dashboardMedia)),
          ))
      .toList();

  @override
  NavigationStack get currentConfiguration => stack;

  @override
  Future<bool> popRoute() async {
    return true;
  }

  @override
  Future<void> setNewRoutePath(NavigationStack configuration) async {
    stack.items = configuration.items;
  }
}

extension _IndexedIterable<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }
}
