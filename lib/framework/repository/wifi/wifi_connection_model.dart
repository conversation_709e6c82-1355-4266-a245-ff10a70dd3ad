// To parse this JSON data, do
//
//     final wifiConnectionResponseModel = wifiConnectionResponseModelFromJson(jsonString);

import 'dart:convert';

WifiConnectionResponseModel wifiConnectionResponseModelFromJson(String str) => WifiConnectionResponseModel.fromJson(json.decode(str));

String wifiConnectionResponseModelToJson(WifiConnectionResponseModel data) => json.encode(data.toJson());

class WifiConnectionResponseModel {
  String? state;
  String? reason;

  WifiConnectionResponseModel({
    this.state,
    this.reason,
  });

  factory WifiConnectionResponseModel.fromJson(Map<String, dynamic> json) => WifiConnectionResponseModel(
    state: json["state"],
    reason: json["reason"],
  );

  Map<String, dynamic> toJson() => {
    "state": state,
    "reason": reason,
  };
}
