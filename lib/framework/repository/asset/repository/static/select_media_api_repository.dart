import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/default_settings/model/default_setting_model.dart';
import 'package:odigo_offline/framework/repository/asset/contract/select_media_repository.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_client.dart';
import 'package:odigo_offline/objectbox.g.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:path_provider/path_provider.dart';

@LazySingleton(as: SelectMediaRepository, env: Env.environments)
class SelectMediaApiRepository implements SelectMediaRepository {
  ObjectBoxClient<Asset> assetClient = ObjectBoxClient<Asset>();
  ObjectBoxClient<ReachedAsset> reachedAssetClient = ObjectBoxClient<ReachedAsset>();
  ObjectBoxClient<OnWayAsset> onWayAssetClient = ObjectBoxClient<OnWayAsset>();

  @override
  Future saveListInObjectBox({required AssetListResponseModel assetList}) async {
    assetClient.deleteAll();
    AssetListResponseModel assetListResponseModel = assetList;
    List<Asset> existingAudioList = getAssetList(assetType: AssetType.audio);
    List<Asset> newAudioList = assetListResponseModel.audio ?? [];
    for (var oldAudio in existingAudioList) {
      int newIndex = newAudioList.indexWhere((newAudio) => newAudio.assetUuid == oldAudio.assetUuid);
      if (newIndex != -1) {
        int oldIndex = existingAudioList.indexWhere((existingAudio) => existingAudio.assetUuid == oldAudio.assetUuid);
        int existingAudioId = existingAudioList[oldIndex].id;
        existingAudioList[oldIndex] = newAudioList[newIndex];
        existingAudioList[oldIndex].id = existingAudioId;
        newAudioList.removeAt(newIndex);
      }
    }
    await assetClient.addList(newAudioList);
    await assetClient.box.putAndGetManyAsync(existingAudioList, mode: PutMode.update);
    List<Asset> existingVideoList = getAssetList(assetType: AssetType.video);
    List<Asset> newVideoList = assetListResponseModel.video ?? [];
    for (var oldVideo in existingVideoList) {
      int newIndex = newVideoList.indexWhere((newVideo) => newVideo.assetUuid == oldVideo.assetUuid);
      if (newIndex != -1) {
        int oldIndex = existingVideoList.indexWhere((existingVideo) => existingVideo.assetUuid == oldVideo.assetUuid);
        int existingVideoId = existingVideoList[oldIndex].id;
        existingVideoList[oldIndex] = newVideoList[newIndex];
        existingVideoList[oldIndex].id = existingVideoId;
        newVideoList.removeAt(newIndex);
      }
    }
    await assetClient.addList(newVideoList);
    await assetClient.box.putAndGetManyAsync(existingVideoList, mode: PutMode.update);
    List<Asset> existingImageList = getAssetList(assetType: AssetType.image);
    List<Asset> newImageList = assetListResponseModel.image ?? [];
    for (var oldImage in existingImageList) {
      int newIndex = newImageList.indexWhere((newImage) => newImage.assetUuid == oldImage.assetUuid);
      if (newIndex != -1) {
        int oldIndex = existingImageList.indexWhere((existingImage) => existingImage.assetUuid == oldImage.assetUuid);
        int existingImageId = existingImageList[oldIndex].id;
        existingImageList[oldIndex] = newImageList[newIndex];
        existingImageList[oldIndex].id = existingImageId;
        newImageList.removeAt(newIndex);
      }
    }
    await assetClient.addList(newImageList);
    await assetClient.box.putAndGetManyAsync(existingImageList, mode: PutMode.update);
  }

  @override
  List<Asset> getAssetList({AssetType? assetType}) {
    List<Asset> assetList = [];
    if (assetType != null) {
      assetList = assetClient.box.query(Asset_.assetType.equals(assetType.name.toUpperCase())).build().find();
    } else {
      assetList = (assetClient.box.getAll());
    }
    return assetList;
  }

  @override
  List<Asset> getAssetListFromListAssetType({List<AssetType>? assetType}) {
    List<Asset> assetList = [];
    if (assetType != null) {
      assetList = assetClient.box.query(Asset_.assetType.oneOf(assetType.map((e) => e.name.toUpperCase()).toList())).build().find();
    } else {
      assetList = (assetClient.box.getAll());
    }
    return assetList;
  }

  @override
  Future addAsset(Asset asset) async {
    final responseModel = await assetClient.add(asset);
    return responseModel;
  }

  @override
  Future<List<Asset>> addAssetList(List<Asset> asset) async {
    final responseModel = (await assetClient.addList(asset)).data ?? [];
    return responseModel;
  }

  @override
  bool deleteAsset(int assetId) {
    var asset = assetClient.box.get(assetId);
    if (asset != null) {
      if (File(asset.assetPath!).existsSync()) File(asset.assetPath!).deleteSync();
      if (File(asset.assetThumbnail ?? '').existsSync()) File(asset.assetThumbnail!).deleteSync();
      final responseModel = assetClient.box.remove(assetId);
      return responseModel;
    }
    return false;
  }

  @override
  bool deleteAssetList(List<int> assetId) {
    assetClient.box.removeMany(assetId);
    return false;
  }

  @override
  bool deleteAssetFromUuid(String assetUuid) {
    var asset = assetClient.box.query(Asset_.assetUuid.equals(assetUuid)).build().findFirst();
    if (asset != null) {
      if (File(asset.assetPath!).existsSync()) File(asset.assetPath!).deleteSync();
      if (File(asset.assetThumbnail ?? '').existsSync()) File(asset.assetThumbnail!).deleteSync();
      final responseModel = assetClient.box.query(Asset_.assetUuid.equals(assetUuid)).build().remove();
      return responseModel == 1;
    }
    return false;
  }

  @override
  void deleteAssetFromAssetType(AssetType assetType) {
    Directory directory = Directory('$applicationDirectoryPath/Assets/${assetType.name.toUpperCase()}');
    if (directory.existsSync()) {
      directory.delete(recursive: true);
    }
    assetClient.box.query(Asset_.assetType.equals(assetType.name.toUpperCase())).build().remove();
  }

  @override
  Asset? getAsset(String assetUuid) {
    final query = assetClient.box.query(Asset_.assetUuid.equals(assetUuid)).build();
    Asset? asset = query.findFirst();
    return asset;
  }

  @override
  void deleteAllAsset() {
    onWayAssetClient.box.removeAll();
    reachedAssetClient.box.removeAll();
    Directory directory = Directory('$applicationDirectoryPath/Assets');
    if (directory.existsSync()) {
      directory.deleteSync(recursive: true);
    }
    assetClient.box.removeAll();
  }

  @override
  List<Asset> fetchAssetFromUuidList(List<String> uuidList) {
    return assetClient.box.query(Asset_.assetUuid.oneOf(uuidList)).build().find();
  }
}
