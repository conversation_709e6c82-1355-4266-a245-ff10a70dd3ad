import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/route/contract/route_repository.dart';
import 'package:odigo_offline/framework/repository/route/model/route_asset_model.dart';
import 'package:odigo_offline/framework/repository/route/model/route_model.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_client.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_response_model.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';

import '../../../../objectbox.g.dart';

@LazySingleton(as: RouteRepository, env: Env.environments)
class RouteApiRepository implements RouteRepository {
  /// object of objectBoxClient
  ObjectBoxClient<RouteModel> routeClient = ObjectBoxClient<RouteModel>();
  ObjectBoxClient<RouteAssetList> routeAssetClient = ObjectBoxClient<RouteAssetList>();

  @override
  Future<void> setRouteList(List<String> routes) async {
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    if (routeModel != null) {
      routeModel.routeList = routes;
      await routeClient.update(routeModel.id, routeModel);
    } else {
      RouteModel routeModel = RouteModel(routeList: routes);
      await routeClient.add(routeModel);
    }
  }

  @override
  Future<void> updateSelectedRoute(String route) async {
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    if (routeModel != null) {
      routeModel.selectedRoute = route;
      routeClient.update(routeModel.id, routeModel);
    } else {
      RouteModel routeModel = RouteModel(selectedRoute: route);
      routeClient.add(routeModel);
    }
  }

  @override
  Future<String?> getSelectedRoute() async {
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    return routeModel?.selectedRoute;
  }

  @override
  Future<List<String>?> getRouteList() async {
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    return routeModel?.routeList;
  }

  @override
  Future<void> setRouteJson(String routeJson) async {
    await routeClient.deleteAll();
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    if (routeModel != null) {
      routeModel.routeJson = routeJson;
      routeClient.update(routeModel.id, routeModel);
    } else {
      RouteModel routeModel = RouteModel(routeJson: routeJson);
      routeClient.add(routeModel);
    }
  }

  @override
  Future<String?> getRouteData() async {
    RouteModel? routeModel = routeClient.box.getAll().firstOrNull;
    return routeModel?.routeJson;
  }

  // @override
  // Future addUpdateAsset(RouteAssetList? routeAssetModel) async {
  //   ///Add Video
  //   if(routeAssetClient.box.getAll().isEmpty){
  //     routeAssetClient.box.put(routeAssetModel!);
  //   }else{
  //     /// Update moving to media
  //     routeAssetClient.box.put(routeAssetModel!);
  //   }
  // }

  @override
  Future addUpdateAsset(RouteAssetList routeAssetModel) async {
    RouteAssetList? routeModel = routeAssetClient.box.getAll().firstOrNull;
    if (routeModel != null) {
      routeModel.video.clear();
      routeModel.image.clear();
      routeModel.video.addAll(routeAssetModel.video);
      routeModel.image.addAll(routeAssetModel.image);
      print(routeModel.toJson());
      await routeAssetClient.update(routeModel.id, routeModel);
    } else {
      await routeAssetClient.add(routeAssetModel);
    }
  }

  @override
  Future<RouteAssetList?> getAssetsDetails() async {
    try {
      RouteAssetList? routeModel = routeAssetClient.box.getAll().firstOrNull;
      if (routeModel != null) {
        return routeModel;
      } else {
        return (await routeAssetClient.add(RouteAssetList())).data;
      }
    } catch (e) {
      showLog("Error deleting location: $e");
      return null;
    }
    return null;
  }
}
