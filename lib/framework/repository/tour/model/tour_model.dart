import 'package:objectbox/objectbox.dart';
import 'package:odigo_offline/framework/repository/tour/model/tour_locations_model.dart';
import 'package:uuid/uuid.dart';

// TourModel entity
@Entity()
class TourModel {
  @Id(assignable: true)
  int id;
  String? tourName;
  String? tourUuid = const Uuid().v8();
  var locationMapping = ToMany<TourLocationsModel>();

  TourModel({
    this.id = 0,
    this.tourName,
    this.tourUuid,
  });

  // Factory method for JSON parsing
  factory TourModel.fromJson(Map<String, dynamic> json) => TourModel(
        tourName: json["tourName"],
      )..locationMapping.addAll(
          json["locationMapping"] == null ? [] : List<TourLocationsModel>.from(json["locationMapping"]!.map((x) => TourLocationsModel.fromJson(x))),
        );

  // Convert to JSON
  Map<String, dynamic> toJson() => {"tourName": tourName, "locationMapping": List<dynamic>.from(locationMapping.map((x) => x.toJson()))};
}
