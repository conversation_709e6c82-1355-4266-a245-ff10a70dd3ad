import 'package:objectbox/objectbox.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';

@Entity()
class DefaultSettingModel {
  @Id()
  int id;
  var defaultOnWayAsset = ToOne<OnWayAsset>();
  var defaultReachedAsset = ToOne<ReachedAsset>();

  DefaultSettingModel({
    this.id = 0,
  });

  factory DefaultSettingModel.fromJson(Map<String, dynamic> json) => DefaultSettingModel()
    ..defaultOnWayAsset.target = json["defaultOnWayAsset"] == null ? null : OnWayAsset.fromJson(json["defaultOnWayAsset"])
    ..defaultReachedAsset.target = json["defaultReachedAsset"] == null ? null : ReachedAsset.fromJson(json["defaultReachedAsset"]);

  Map<String, dynamic> toJson() => {
        "id": id,
        "defaultOnWayAsset": defaultOnWayAsset.target?.toJson(),
        "defaultReachedAsset": defaultReachedAsset.target?.toJson(),
      };
}

@Entity()
class OnWayAsset {
  @Id()
  int id;
  @Transient()
  String? selectedMediaType;

  var audio = ToOne<Asset>();
  var video = ToOne<Asset>();
  var image = ToOne<Asset>();

  OnWayAsset({this.id = 0, this.selectedMediaType});

  factory OnWayAsset.fromJson(Map<String, dynamic> json) => OnWayAsset()..selectedMediaType = json["selectedMediaType"];

  Map<String, dynamic> toJson() => {
        "id": id,
        "audio": audio.target?.toJson(),
        "video": video.target?.toJson(),
        "image": image.target?.toJson(),
        "selectedMediaType": selectedMediaType,
      };
}

@Entity()
class ReachedAsset {
  @Id()
  int id;
  String? selectedMediaType;

  var audio = ToOne<Asset>();
  var video = ToOne<Asset>();
  var image = ToOne<Asset>();

  ReachedAsset({
    this.id = 0,
    this.selectedMediaType,
  });

  factory ReachedAsset.fromJson(Map<String, dynamic> json) => ReachedAsset()..selectedMediaType = json["selectedMediaType"];

  Map<String, dynamic> toJson() => {
        "audio": audio.target?.toJson(),
        "video": video.target?.toJson(),
        "image": image.target?.toJson(),
        "selectedMediaType": selectedMediaType,
      };
}
