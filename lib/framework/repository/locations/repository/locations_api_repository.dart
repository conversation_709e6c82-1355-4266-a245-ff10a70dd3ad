import 'package:collection/collection.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_point_model.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/locations/contract/location_repository.dart';
import 'package:odigo_offline/framework/repository/route/model/route_asset_model.dart';
import 'package:odigo_offline/framework/repository/route/model/route_model.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/framework/repository/tour/model/tour_locations_model.dart';
import 'package:odigo_offline/framework/repository/tour/model/tour_model.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_client.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_response_model.dart';
import 'package:odigo_offline/objectbox.g.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';

@LazySingleton(as: LocationRepository, env: Env.environments)
class LocationsApiRepository implements LocationRepository {
  LocationsApiRepository();

  ObjectBoxClient<Asset> assetClient = ObjectBoxClient<Asset>();

  /// object of objectBoxClient
  ObjectBoxClient<LocationModel> locationDataClient = ObjectBoxClient<LocationModel>();
  ObjectBoxClient<LocationPoint> locationPointDataClient = ObjectBoxClient<LocationPoint>();
  ObjectBoxClient<OnWayAssetList> movingToMediaClient = ObjectBoxClient<OnWayAssetList>();
  ObjectBoxClient<ReachedAssetList> reachedToMediaClient = ObjectBoxClient<ReachedAssetList>();
  ObjectBoxClient<TourModel> tourClient = ObjectBoxClient<TourModel>();
  ObjectBoxClient<TourLocationsModel> tourLocationClient = ObjectBoxClient<TourLocationsModel>();
  ObjectBoxClient<RouteModel> routeClient = ObjectBoxClient<RouteModel>();
  ObjectBoxClient<RouteAssetList> routeAssetClient = ObjectBoxClient<RouteAssetList>();

  @override
  Future<LocationPoint?> updateLocationPoint(String oldName, String newName) async {
    LocationPoint? oldPoint = locationPointDataClient.box.query(LocationPoint_.name.equals(oldName)).build().findFirst();
    if (oldPoint != null) {
      oldPoint.name = newName;
      locationPointDataClient.box.put(oldPoint);
    }
    return oldPoint;
  }

  @override
  Future<List<LocationPoint>> saveLocationPointInObjectBox({required List<LocationPoint> points}) async {
    List<LocationPoint> oldPoints = locationPointDataClient.box.getAll();
    List<int> removeIds = oldPoints.map((e) => e.id).toList();
    for (var point in points) {
      int? id = oldPoints.where((element) => element.name == point.name).firstOrNull?.id;
      if (id != null) {
        removeIds.remove(id);
      }
    }
    oldPoints = locationPointDataClient.box.getAll();
    await locationPointDataClient.box.removeManyAsync(removeIds);
    for (var oldPoint in oldPoints) {
      points.removeWhere((element) => element.name == oldPoint.name);
    }
    return (await locationPointDataClient.addList(points)).data ?? [];
  }

  @override
  List<LocationPoint> getLocationPointList({LocationPointType? type}) {
    List<LocationPoint> locationPointList = [];
    if (type != null) {
      Query<LocationPoint> query = locationPointDataClient.box.query(LocationPoint_.type.equals(type.name)).build();
      locationPointList = query.find();
    } else {
      locationPointList = (locationPointDataClient.box.getAll());
    }

    return locationPointList;
  }

  @override
  Future<void> setupNewMap() async {
    locationPointDataClient.deleteAll();
    routeClient.deleteAll();
    routeAssetClient.deleteAll();
    locationDataClient.deleteAll();
    tourClient.deleteAll();
    tourLocationClient.deleteAll();
    movingToMediaClient.deleteAll();
    reachedToMediaClient.deleteAll();
  }

  @override
  List<LocationModel> getLocationList({LocationPointType? type}) {
    List<LocationModel> locationList = locationDataClient.box.getAll();
    if (type != null) {
      locationList.removeWhere((element) => element.locationPoint.target?.type != type.name);
    }
    return locationList;
  }

  @override
  Future<void> setLocationData(LocationModel? locationData) {
    List<LocationModel>? locationListData = locationDataClient.box.getAll();
    if (locationListData.contains(locationData)) {
      return locationDataClient.update(locationData?.id ?? 0, locationData!);
    } else {
      return locationDataClient.add(locationData!);
    }
  }

  @override
  Future addUpdateNewLocation({int? locationId, required LocationModel location}) async {
    if (locationId != null) {
      /// Get Location from id
      LocationModel? entity = locationDataClient.box.get(locationId);
      if (entity != null) {
        TourLocationsModel? tourLocation = tourLocationClient.box.query(TourLocationsModel_.locationUuid.equals(entity.locationUuid!)).build().findFirst();
        if (tourLocation != null) {
          tourLocation.locationPoint = entity.locationPoint.target?.name;
          tourLocationClient.box.put(tourLocation);
        }
      }

      /// Update entity with new one
      entity = location;

      //-------------- Updating moving to media -------------

      /// Get moving to media
      int movingToMediaId = entity.onWayAsset.target?.id ?? -1;
      if (location.onWayAsset.target != null) {
        if (movingToMediaId != -1) {
          /// Get old moving to media based on id
          OnWayAssetList? movingToMediaNew = movingToMediaClient.box.get(movingToMediaId);

          /// Assign old moving to media with new one
          movingToMediaNew = location.onWayAsset.target;

          /// Update moving to media
          movingToMediaClient.box.put(movingToMediaNew!);

          OnWayAssetList? updated = movingToMediaClient.box.get(movingToMediaId);
          debugPrint("Updated moving to ${updated?.toJson()}");
        } else {
          debugPrint("Moving to media id not found");
        }
      }

      //-------------- Updating reached to media -------------

      /// Get moving to media
      int reachToMediaId = entity.reachedAsset.target?.id ?? -1;
      if (location.reachedAsset.target != null) {
        if (reachToMediaId != -1) {
          /// Get old reach to media based on id
          ReachedAssetList? reachedToMediaOld = reachedToMediaClient.box.get(reachToMediaId);

          /// Assign old reach to media with new one
          reachedToMediaOld = location.reachedAsset.target;

          /// Update reach to media
          reachedToMediaClient.box.put(reachedToMediaOld!);

          ReachedAssetList? updated = reachedToMediaClient.box.get(reachToMediaId);
          debugPrint("Updated reach to ${updated?.toJson()}");
        } else {
          debugPrint("Moving to media id not found");
        }
      }

      await locationDataClient.box.putAndGetAsync(entity, mode: PutMode.update);
      LocationModel? updatedEntity = locationDataClient.box.get(locationId);
      debugPrint("Updated entity JSON: ${updatedEntity?.toJson()}");
      return updatedEntity;
    } else {
      var res = await locationDataClient.add(location);
      return res;
    }
  }

  /// get location details

  /// Get setting added location points
  @override
  Future getSettingAddedLocationPoints({required LocationPointType type}) async {
    Query<LocationModel> query = locationDataClient.box.query().build();
    List<LocationModel> locationModels = query.find();
    List<LocationModel> filteredLocations = locationModels.where((location) {
      return location.locationPoint.target?.type == type.name;
    }).toList();
    return filteredLocations.firstOrNull;
  }

  @override
  LocationModel? getLocationDetails({required locationId}) {
    try {
      LocationModel? response = locationDataClient.box.get(locationId);

      if (response != null) {
        showLog("Got location with ID: $locationId");
        return response;
      } else {
        showLog("Location with ID: $locationId not found");
        return null;
      }
    } catch (e) {
      showLog("Error deleting location: $e");
      return null;
    }
  }

  /// Delete locations
  @override
  Future deleteLocation({required String? locationUuid}) async {
    try {
      var tourList = tourClient.box.getAll();
      List<TourModel> tourContainingLocation = tourList.where((element) => element.locationMapping.where((element) => element.locationUuid == locationUuid).firstOrNull != null).toList();
      tourClient.box.removeMany(tourContainingLocation.map((e) => e.id).toList());
      List<int> tourLocationIds = [];
      for (var tour in tourContainingLocation) {
        tourLocationIds.addAll(tourLocationClient.box.query(TourLocationsModel_.tourUuid.equals(tour.tourUuid ?? '')).build().findIds());
      }
      tourLocationClient.box.removeMany(tourLocationIds);
      locationDataClient.box.query(LocationModel_.locationUuid.equals(locationUuid ?? '')).build().remove();
    } catch (e) {
      showLog("Error deleting location: $e");
      return ObjectBoxResponseModel<bool>(success: SuccessType.ERROR);
    }
  }

  @override
  Future<void> deleteAllData() async {
    locationDataClient.deleteAll();
    movingToMediaClient.deleteAll();
    reachedToMediaClient.deleteAll();
  }
}
