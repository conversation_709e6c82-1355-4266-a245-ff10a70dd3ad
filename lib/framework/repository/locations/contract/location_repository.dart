import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_point_model.dart';

import 'package:odigo_offline/ui/utils/const/app_enums.dart';

abstract class LocationRepository {
  /// Set Location
  Future setLocationData(LocationModel? locationData);

  Future<void> setupNewMap();

  /// Get Location
  List<LocationModel> getLocationList({LocationPointType? type});

  Future<LocationPoint?> updateLocationPoint(String oldName, String newName);

  /// Save location point in object box
  Future<List<LocationPoint>> saveLocationPointInObjectBox({required List<LocationPoint> points});

  /// Add Location
  Future addUpdateNewLocation({int? locationId, required LocationModel location});

  /// Get Location point list
  List<LocationPoint> getLocationPointList({LocationPointType? type});

  /// To Delete Location By ID
  Future deleteLocation({required String? locationUuid});

  /// To get production and charging point for setting module
  Future getSettingAddedLocationPoints({required LocationPointType type});

  /// Get location details
  LocationModel? getLocationDetails({required locationId});

  Future<void> deleteAllData();
}
