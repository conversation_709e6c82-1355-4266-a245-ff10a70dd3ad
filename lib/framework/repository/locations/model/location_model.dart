import 'package:objectbox/objectbox.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_point_model.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:uuid/uuid.dart';

@Entity()
class LocationModel {
  @Id()
  int id = 0; // Auto-incrementing ID for ObjectBox.

  @Unique(onConflict: ConflictStrategy.replace)
  String? locationName;
  String? locationUuid = const Uuid().v8();

  // Relation to LocationPoint
  var locationPoint = ToOne<LocationPoint>();

  // Relation to MovingToMedia
  var onWayAsset = ToOne<OnWayAssetList>();

  // Relation to ReachedToMedia
  var reachedAsset = ToOne<ReachedAssetList>();

  LocationModel({
    this.locationName,
    this.locationUuid,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    final model = LocationModel(
      locationName: json['locationName'],
      locationUuid: json['locationUuid'],
    );

    if (json['locationPoint'] != null) {
      model.locationPoint.target = json['locationPoint'] == null ? null : LocationPoint.fromJson(json['locationPoint']);
    }
    if (json['onWayAsset'] != null) {
      model.onWayAsset.target = json['onWayAsset'] == null ? null : OnWayAssetList.fromJson(json['onWayAsset']);
    }
    if (json['reachedAsset'] != null) {
      model.reachedAsset.target = json['reachedAsset'] == null ? null : ReachedAssetList.fromJson(json['reachedAsset']);
    }

    model.id = json['id'] ?? 0;

    return model;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'locationName': locationName,
      'locationUuid': locationUuid,
      'locationPoint': locationPoint.target?.toJson(),
      'onWayAsset': onWayAsset.target?.toJson(),
      'reachedAsset': reachedAsset.target?.toJson(),
    };
  }
}

@Entity()
class OnWayAssetList {
  @Id()
  int id = 0;

  String? selectedMediaType;

  var audio = ToMany<Asset>();
  var video = ToMany<Asset>();
  var image = ToMany<Asset>();

  OnWayAssetList({
    this.selectedMediaType,
  });

  factory OnWayAssetList.fromJson(Map<String, dynamic> json) {
    final list = OnWayAssetList(
      selectedMediaType: json['selectedMediaType'],
    );
    list.id = json['id'] ?? 0;

    if (json['audio'] != null) {
      list.audio.addAll(
        (json['audio'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }
    if (json['video'] != null) {
      list.video.addAll(
        (json['video'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }
    if (json['image'] != null) {
      list.image.addAll(
        (json['image'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }

    return list;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'selectedMediaType': selectedMediaType,
      'audio': audio.map((e) => e.toJson()).toList(),
      'video': video.map((e) => e.toJson()).toList(),
      'image': image.map((e) => e.toJson()).toList(),
    };
  }
}

@Entity()
class ReachedAssetList {
  @Id()
  int id = 0;
  String? selectedMediaType;

  // Relation to Asset
  var audio = ToMany<Asset>();
  var video = ToMany<Asset>();
  var image = ToMany<Asset>();

  ReachedAssetList({
    this.selectedMediaType,
  });

  factory ReachedAssetList.fromJson(Map<String, dynamic> json) {
    final list = ReachedAssetList(
      selectedMediaType: json['selectedMediaType'],
    );
    list.id = json['id'] ?? 0;

    if (json['audio'] != null) {
      list.audio.addAll(
        (json['audio'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }
    if (json['video'] != null) {
      list.video.addAll(
        (json['video'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }
    if (json['image'] != null) {
      list.image.addAll(
        (json['image'] as List).map((e) => Asset.fromJson(e)).toList(),
      );
    }

    return list;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'selectedMediaType': selectedMediaType,
      'audio': audio.map((e) => e.toJson()).toList(),
      'video': video.map((e) => e.toJson()).toList(),
      'image': image.map((e) => e.toJson()).toList(),
    };
  }
}
