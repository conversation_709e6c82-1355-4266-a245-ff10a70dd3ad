// To parse this JSON data, do
//
//     final locationPointModel = locationPointModelFromJson(jsonString);

import 'dart:convert';
import 'package:objectbox/objectbox.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:uuid/uuid.dart';

LocationPointResponseModel locationPointModelFromJson(String str) => LocationPointResponseModel.fromJson(json.decode(str));

String locationPointModelToJson(LocationPointResponseModel data) => json.encode(data.toJson());

class LocationPointResponseModel {
  List<LocationPoint>? waypoints;

  LocationPointResponseModel({
    this.waypoints,
  });

  factory LocationPointResponseModel.fromJson(Map<String, dynamic> json) => LocationPointResponseModel(
        waypoints: json["waypoints"] == null ? [] : List<LocationPoint>.from(json["waypoints"]!.map((x) => LocationPoint.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "waypoints": waypoints == null ? [] : List<dynamic>.from(waypoints!.map((x) => x.toJson())),
      };
}
// To parse this JSON data, do
//
//     final locationPointModel = locationPointModelFromJson(jsonString);

@Entity()
class LocationPoint {
  @Id() // Primary key for ObjectBox
  int id = 0;
  String? uuid = const Uuid().v8();
  String? name;
  String? type;

  LocationPoint({
    this.id = 0,
    this.name,
    this.type,
    this.uuid,
  });

  factory LocationPoint.fromJson(Map<String, dynamic> json) => LocationPoint(
    id: json["id"],
    name: json["name"],
    type: json["type"],
    uuid: json["uuid"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "uuid": uuid,
    "name": name,
    "type": type,
  };
}

