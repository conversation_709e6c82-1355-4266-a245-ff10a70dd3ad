import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.config.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureMainDependencies({required String environment}) async {
  if (!getIt.isRegistered<String>(instanceName: 'mainInstance')) {
    getIt.registerSingleton<String>(environment, instanceName: 'mainInstance');
    GetItInjectableX(getIt).init(environment: environment);
  }
}

String? getEnvironment() => getIt.get<String>(instanceName: 'mainInstance');

abstract class Env {
  static const static = staticEnv;
  static const hsbc = 'hsbc';
  static const environments = [static, hsbc];
}
