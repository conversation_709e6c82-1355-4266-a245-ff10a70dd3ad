import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/ip_map/model/map_list_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/canvas_current_map_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/current_map_name_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/current_map_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/map_response_model.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/datetime_extension.dart';

import 'package:odigo_offline/framework/utils/ui_state.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';

final mapController = ChangeNotifierProvider((ref) => getIt<MapController>());

@injectable
class MapController extends ChangeNotifier {
  RobotEventManger robotEventManger = RobotEventManger.instance;

  MapController();

  MapData? selectedMap;
  IpMapElement? selectedIpMap;

  List<String> mapTabList = [];

  initMapList() {
    mapTabList = [LocaleKeys.keyLocalMaps];
    selectedMapTab = LocaleKeys.keyLocalMaps;
  }

  String selectedMapTab = '';

  updateSelectedMapTab(String selectedMapTab) {
    this.selectedMapTab = selectedMapTab;
    notifyListeners();
  }

  updateSelectedIpMap(IpMapElement? selectedIpMap) {
    this.selectedIpMap = selectedIpMap;
    notifyListeners();
  }

  updateWidget() {
    notifyListeners();
  }

  String aliasName = DateTime.now().toFormattedDateTime;

  updateMapAliasName(String value) {
    aliasName = value;
    notifyListeners();
  }

  bool applyingMap = false;

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    notifyListeners();
  }

  selectMap(MapData? map) {
    selectedMap = map;
    notifyListeners();
  }

  ///----------Audio--------///
  GlobalKey<FormState> pointNameKey = GlobalKey<FormState>();
  GlobalKey<FormState> routeNameKey = GlobalKey<FormState>();
  GlobalKey commonTipsDialogKey = GlobalKey();
  GlobalKey commonLoadingDialogKey = GlobalKey();
  GlobalKey savingMapDialogKey = GlobalKey();
  GlobalKey commonDialogKey = GlobalKey();
  TextEditingController pointNameController = TextEditingController();
  String pointName = '';
  TextEditingController routeNameController = TextEditingController();
  var currentPosition = [];
  bool isInitMap = false;
  MultipartFile? demo;

  GlobalKey mapLoadingKey = GlobalKey(debugLabel: 'deleteMapKey');
  GlobalKey mainDialogKey = GlobalKey(debugLabel: 'mainDialogKey');

  StreamSubscription<ApplyMapEvent>? applyMapSubscription;

  Future<void> applyMapEvent(BuildContext context, WidgetRef ref, {String? mapName, Function()? afterMapApply}) async {
    showLog('mapName On Apply Map Event');
    ipCurrentMap(
      onMapGet: (mapName) async {
        print(mapName);
        print('${selectedIpMap?.name}');
        print('${ipCurrentMapState.success?.map}');
        if (ipCurrentMapState.success?.map == selectedIpMap?.name) {
          applyingMap = false;
          Navigator.pop(context);
          RobotMetaDataRepository.instance.currentMap = mapName;
          afterMapApply?.call();
        } else {
          applyingMap = true;
          robotEventManger.applyMap(mapName!);
          applyMapSubscription = robotEventManger.applyMapStream.stream.listen((event) {
            showLog('applyingMap  applyMapEventApi $applyingMap');
            if (applyingMap) {
              applyingMap = false;
              if (event.success ?? false) {
                RobotMetaDataRepository.instance.currentMap = mapName;
                applyingMap = false;
                Navigator.pop(context);
                applyMapSubscription?.cancel();
                afterMapApply?.call();
                notifyListeners();
              }
            } else {
              Navigator.pop(context);
            }
          });
        }
      },
    );
    notifyListeners();
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  UIState<MapListResponseModel> ipMapListData = UIState<MapListResponseModel>();
  IpMapElement? currentMap;

  Future<void> ipMapListDataApi(BuildContext context, {bool removeSyncedMap = false}) async {
    ipMapListData.isLoading = true;
    ipCurrentMapData.isLoading = true;
    notifyListeners();
    ipMapListData.success = await CanvasMapApiClass.api.getMapList();
    ipCurrentMapData.success = await CanvasMapApiClass.api.getCurrentMapName();
    ipMapListData.isLoading = false;
    ipCurrentMapData.isLoading = false;
    currentMap = ipMapListData.success?.maps?.where((element) => element.name == ipCurrentMapData.success?.name).firstOrNull;
    ipMapListData.success?.maps?.removeWhere((element) => element.name == ipCurrentMapData.success?.name);
    notifyListeners();
  }

  UIState<CurrentMapNameResponseModel> ipCurrentMapData = UIState<CurrentMapNameResponseModel>();


  UIState<MapEvent> ipCurrentMapState = UIState<MapEvent>();
  StreamSubscription<MapEvent>? mapSubscription;

  void ipCurrentMap({required Function(String? mapName) onMapGet}) {
    ipCurrentMapState.isLoading = true;
    notifyListeners();
    robotEventManger.getCurrentMap();
    mapSubscription = robotEventManger.mapStream.stream.listen((event) {
      ipCurrentMapState.success = event;
      ipCurrentMapState.isLoading = false;
      mapSubscription?.cancel();
      mapSubscription = null;
      notifyListeners();
      onMapGet.call(ipCurrentMapState.success?.map);
    });
  }

  TextEditingController aliasNameController = TextEditingController();

  @override
  void notifyListeners() {
    super.notifyListeners();
  }
}
