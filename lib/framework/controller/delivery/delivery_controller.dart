import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_point_model.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/locations/contract/location_repository.dart';
import 'package:odigo_offline/framework/repository/meal_delivery/contract/meal_delivery_repository.dart';
import 'package:odigo_offline/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/utils/extension/map_extension.dart';

import 'package:odigo_offline/framework/utils/ui_state.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:uuid/uuid.dart';

final deliveryController = ChangeNotifierProvider((ref) => getIt<DeliveryController>());

@injectable
class DeliveryController extends ChangeNotifier {
  DeliveryRepository deliveryRepository;
  final LocationRepository locationRepository;

  DeliveryController(this.deliveryRepository, this.locationRepository);

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    audioIndex = 0;
    if (isNotify) {
      notifyListeners();
    }
  }

  int audioIndex = 0;

  void updateAudioIndex() {
    audioIndex++;
    notifyListeners();
  }

  ///List of location points (demo)
  List<Waypoint> points = [];

  var wayPointsState = UIState<DeliveryPointsResponseModel>();

  Future<void> getDeliveryPointsApi(BuildContext context) async {
    wayPointsState.isLoading = true;
    wayPointsState.success = null;
    notifyListeners();

    wayPointsState.success = await deliveryRepository.getPointsList();
    points.clear();
    points = wayPointsState.success?.waypoints ?? [];
    List<LocationPoint> locationPointList = [];
    for (var point in points) {
      locationPointList.add(LocationPoint(name: point.name, type: point.type, uuid: const Uuid().v8()));
    }
    locationRepository.saveLocationPointInObjectBox(points: locationPointList);
    points.removeWhere((element) => element.type == PointTypeEnum.production.name);
    points.removeWhere((element) => element.type == PointTypeEnum.charge.name);
    points.removeWhere((element) => element.type == PointTypeEnum.recycle.name);
    points.sort((Waypoint pointName1, Waypoint pointName2) {
      return ((pointName1.name).toLowerCase()).compareTo((pointName2.name).toLowerCase());
    });
    wayPointsState.isLoading = false;
    notifyListeners();
  }

  Future<void> getRouteList(BuildContext context) async {
    wayPointsState.isLoading = true;
    wayPointsState.success = null;
    notifyListeners();
    final result = await deliveryRepository.getRoutesList();
    Map<String, List<List<double>>> responseMap = (result as Map<String, dynamic>).toRouteMap;
    RobotMetaDataRepository.instance.routes = responseMap;
    wayPointsState.isLoading = false;
    notifyListeners();
  }
}
