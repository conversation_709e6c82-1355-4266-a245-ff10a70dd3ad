import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_painter_controller.dart';
import 'package:odigo_offline/framework/controller/canvas_map/graph_extension.dart';
import 'package:odigo_offline/framework/controller/map/map_controller.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_offline/framework/repository/map/model/canvas_current_map_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/current_map_name_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/laser_data_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/map_variables.dart';
import 'package:odigo_offline/framework/repository/map/model/relocation_response_model.dart';
import 'package:odigo_offline/framework/repository/map/model/virtual_wall_response_model.dart';
import 'package:odigo_offline/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_offline/ui/canvas_map/helper/custom_painter_canvas.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui' as ui;

import 'package:flutter_svg_custom/flutter_svg.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';

final canvasMapController = ChangeNotifierProvider((ref) => CanvasMapController());

class CanvasMapController extends ChangeNotifier {
  String currentMap = '';
  int currentMapMode = 0;
  int currentNavigationMode = 0;
  String title = '';
  String message = '';
  String pointName = '';
  bool showNavigationToast = false;
  bool isDasherConnected = true;

  ///Get Current Map
  Future<void> getCurrentMap() async {
    CanvasCurrentMapResponseModel? currentMapResponseModel = await CanvasMapApiClass.api.getCurrentMap();
    if (currentMapResponseModel == null) {
      isDasherConnected = false;
      notifyListeners();
    } else {
      currentMap = currentMapResponseModel.imageUrl ?? '';
      var originX = currentMapResponseModel.originX ?? 0.0;
      var originY = currentMapResponseModel.originY ?? 0.0;
      var resolution = currentMapResponseModel.resolution ?? 0.0;
      var width = currentMapResponseModel.width ?? 0.0;
      var height = currentMapResponseModel.height ?? 0.0;
      var centerX = (originX / resolution) * -1;
      var centerY = (height) - ((originY / resolution) * -1);
      MapVariables.map.initializeMapConstants(currentMap, originX, originY, resolution, width, height, centerX, centerY);
      if (currentMapMode == 1) {
        clearAllData();
      }
      image = await MapVariables.map.loadImage(base64Decode(currentMap.replaceAll('data:image/png;base64,', '')), doChangeColor: true);
      isDasherConnected = true;
      refreshMapPainter();
    }
  }

  String currentMapName = '';
  CurrentMapNameResponseModel? currentMapNameResponseModel;

  ///Get Current Map Name
  Future<void> getCurrentMapName() async {
    currentMapNameResponseModel = await CanvasMapApiClass.api.getCurrentMapName();
    notifyListeners();
  }

  void clearAllData() {
    waypointsList.clear();
    naviRoutes.clear();
    laserData.clear();
    threeDData.clear();
    virtualWall.clear();
    refreshEntireCanvas();
  }

  ///List of available points
  List<Waypoint> waypointsList = [];
  List<Waypoint> searchedWaypointList = [];
  TextEditingController searchedWaypointController = TextEditingController();
  PointType typeValue = PointType.ALL;

  final List<PointType> deliveryPointList = [
    PointType.ALL,
    PointType.DELIVERY,
    PointType.PRODUCTION,
    PointType.CHARGE,
  ];

  void changeTypes(PointType value) {
    typeValue = value;
    updateSearchedWaypointList();
  }

  void updateSearchedWaypointList({bool isCancel = false}) {
    if (isCancel) {
      typeValue = PointType.ALL;
      searchedWaypointController.clear();
    }
    if (searchedWaypointController.text.isNotEmpty || typeValue != PointType.ALL) {
      searchedWaypointList =
          waypointsList.where((element) => (element.name.toLowerCase().contains(searchedWaypointController.text.toLowerCase()) && (typeValue != PointType.ALL ? (element.type == typeValue) : true))).toList();
    } else {
      searchedWaypointList = [];
    }
    notifyListeners();
  }

  ///Get way points
  Future<void> getWaypoints() async {
    DeliveryPointsResponseModel? waypoints = await CanvasMapApiClass.api.getWaypoints();
    waypointsList = waypoints?.waypoints ?? [];
    waypointsList.sort((Waypoint pointName1, Waypoint pointName2) {
      return ((pointName1.name).toLowerCase()).compareTo((pointName2.name).toLowerCase());
    });
    refreshWaypointsPainter();
  }

  ///List of navigation routes
  Map<String, List<List<double>>> naviRoutes = {};

  ///Get navigation routes
  Future<void> getNaviRoutes() async {
    naviRoutes = await CanvasMapApiClass.api.getNaviRoutes();
    refreshRoutesPainter();
  }

  ///List of virtual wall
  List<VirtualWallPoint> virtualWall = [];

  ///Get virtual walls
  Future<void> getVirtualWall() async {
    VirtualWallResponseModel? virtualWallModel = await CanvasMapApiClass.api.getVirtualWall();
    virtualWall = virtualWallModel?.waypoints ?? [];
    refreshVirtualWallPainter();
  }

  ///Save changes in virtual wall
  Future<void> saveVirtualWall() async {
    List<VirtualWallPoint> finalPoints = virtualWall + updatedVirtualWallPoints;
    drawVirtualWall = false;
    eraseVirtualWall = false;
    updatedVirtualWallPoints.clear();
    virtualWallPoint = VirtualWallPoint(pose: VirtualWallPose(point1: Point(x: 0, y: 0), point2: Point(x: 0, y: 0)));
    await CanvasMapApiClass.api.saveVirtualWall(VirtualWallResponseModel(waypoints: finalPoints));
    getVirtualWall();
    refreshDrawnVirtualWallPainter();
  }

  ///Laser Sensor Data
  List<List<double>> laserData = [];

  ///Laser Sensor Data
  List<List<double>> threeDData = [];

  ///Global Path when navigation is started
  List<List<double>>? globalPath;

  Timer? laserTimer;

  ///Current Position of Robot
  PositionEvent? currentPosition;
  LaserDataResponseModel? threeDDataModel;
  LaserDataResponseModel? laserDataResponseModel;

  ui.Image? image;
  DrawableRoot? odigoImage;
  DrawableRoot? chargingPointImage;
  DrawableRoot? productionPointImage;
  DrawableRoot? deliveryPointImage;
  DrawableRoot? routeImage;
  ui.Picture? recyclePoint;

  bool isPointShown = true;
  bool isDialogShown = false;

  void updateIsPointShown(bool isPointShown) {
    this.isPointShown = isPointShown;
    notifyListeners();
  }

  bool isVirtualWallShown = true;

  void updateVirtualWallShown(bool isVirtualWallShown) {
    this.isVirtualWallShown = isVirtualWallShown;
    notifyListeners();
  }

  bool isRouteShown = true;

  void updateIsRouteShown(bool isRouteShown) {
    this.isRouteShown = isRouteShown;
    notifyListeners();
  }

  bool is3DDataShown = false;

  void updateIs3DDataShown(bool is3DDataShown) {
    this.is3DDataShown = is3DDataShown;
    notifyListeners();
  }

  RobotEventManger robotEventManger = RobotEventManger.instance;
  StreamSubscription<PositionEvent>? onPositionSubscription;
  bool markPoint = false;
  bool editMode = false;

  updateEditMode(bool editMode) {
    this.editMode = editMode;
    notifyListeners();
  }

  PointType markPointType = PointType.ALL;
  bool finalRoutePoint = false;

  handlePositionObtained(BuildContext context) async {
    await robotEventManger.getMode();
    onPositionSubscription = robotEventManger.positionStream.stream.listen((event) {
      currentPosition = event;
      refreshPositionPainter();
      if (markPoint) {
        if (isDialogShown) {
          Navigator.pop(context);
        }
        markPoint = false;
        String strIcon = '';
        String title = '';
        bool showNameDialog = true;
        switch (markPointType) {
          case PointType.CHARGE:
            strIcon = Assets.svgs.svgOdigoCharge.path;
            title = 'Please Make sure that the robot is correctly\ndocked on the charging pile, and then\nclick confirm to set the charging pile';
            break;
          case PointType.DELIVERY:
            strIcon = Assets.svgs.svgOdigoLocation.path;
            title = 'Are you sure to set the current\nposition as Location point?';
            break;
          case PointType.PRODUCTION:
            strIcon = Assets.svgs.svgOdigoProduction.path;
            title = 'The production point is the starting\nposition of each delivery task. Are you\nsure to set the current position as\nproduction point?';
            break;
          case PointType.ROUTE:
            strIcon = Assets.svgs.svgOdigoLocation.path;
            title = 'Move the robot to the\nposition where you want to mark\ninitial point';
            showNameDialog = false;
            if (newRoutePoints.isNotEmpty) {
              if (finalRoutePoint) {
                addFinalRoutePoint((event.x ?? 0).convertXFromDasherPoint, (event.y ?? 0).convertYFromDasherPoint);
              } else {
                addCreateRoutePoints((event.x ?? 0).convertXFromDasherPoint, (event.y ?? 0).convertYFromDasherPoint);
              }
              commonSnackBar(context, 'Point Marked');
            }
            break;
          default:
            null;
        }
        if (title.isNotEmpty && strIcon.isNotEmpty && newRoutePoints.isEmpty) {
          showMarkPointDialog(
            context,
            strIcon: strIcon,
            title: title,
            pointType: pointTypeValues.reverseMap[markPointType],
            position: currentPosition,
            showNameDialog: showNameDialog,
            onConfirmClicked: () {
              if (markPointType == PointType.ROUTE) {
                updateDrawRoute();
                addCreateRoutePoints((event.x ?? 0).convertXFromDasherPoint, (event.y ?? 0).convertYFromDasherPoint);
              }
            },
          );
        }
      }
    });
  }

  StreamSubscription<SetFlagPointEvent>? onSetFlagPointSubscription;

  handleSetFlagPoint() {
    onSetFlagPointSubscription?.cancel();
    onSetFlagPointSubscription = robotEventManger.setFlagPointStream.stream.listen((event) {
      if (event.result == 0) {
        getWaypoints().then((value) {
          //reDrawCanvas();
        });
      }
    });
  }

  StreamSubscription<DelFlagPointEvent>? onDelFlagPointSubscription;

  handleDelFlagPoint() {
    onDelFlagPointSubscription?.cancel();
    onDelFlagPointSubscription = robotEventManger.delFlagPointStream.stream.listen((event) {
      if (event.result == 0) {
        getWaypoints().then((value) {
          //reDrawCanvas();
        });
      }
    });
  }

  StreamSubscription<NavModeEvent>? onModeSubscription;

  handleModeObtained(BuildContext context) {
    onModeSubscription = robotEventManger.navModeStream.stream.listen((event) {
      currentMapMode = event.mode ?? 0;
      if (isDialogShown) {
        Navigator.pop(context);
        isDialogShown = false;
      }
      if (currentMapMode != 1) {
        clearAllData();
        if (currentMapMode == 2) {
          isRebuildMap = true;
        } else {
          isIncrementMap = true;
        }
      } else {
        getCurrentMap();
        resetInitialPosition();
        getWaypoints();
        getNaviRoutes();
        getVirtualWall();
        String? alias = globalRef?.read(mapController).aliasNameController.text;
        if (alias?.isNotEmpty ?? false) {
          final mapWatch = globalRef!.watch(mapController);
          CanvasMapApiClass.api.renameMap(mapWatch.currentMap!.name ?? '', mapWatch.aliasNameController.text).then((value) => mapWatch.aliasNameController.clear());
        }
      }
      notifyListeners();
    });
  }

  StreamSubscription<bool>? onGetMapSubscription;
  StreamController<bool> getMapStream = StreamController<bool>.broadcast();

  handleGetMapUpdate() {
    onGetMapSubscription?.cancel();
    onGetMapSubscription = getMapStream.stream.listen((event) {
      if (currentMapMode != 1) {
        getCurrentMap();
      }
    });
  }

  StreamSubscription<MapEvent>? onMapSubscription;

  handleMapUpdate(BuildContext context) {
    onMapSubscription?.cancel();
    onMapSubscription = robotEventManger.mapStream.stream.listen((event) {
      if (isDialogShown) {
        Navigator.pop(context);
        isDialogShown = false;
      }
      getCurrentMap();
    });
  }

  StreamSubscription<List<List<double>>>? onLaserSubscription;
  StreamController<List<List<double>>> laserStream = StreamController<List<List<double>>>.broadcast();

  handleLaserStream() {
    onLaserSubscription?.cancel();
    onLaserSubscription = laserStream.stream.listen((event) {
      laserData = event;
      refreshContinousData();
    });
  }

  Future<void> startSystemContinuousMonitoring() async {
    await getCurrentMapName();
    currentMapName = currentMapNameResponseModel?.name ?? '';
    laserDataResponseModel = await CanvasMapApiClass.api.getLaserData();
    laserStream.add(laserDataResponseModel?.coordinates ?? []);
    laserTimer?.cancel();
    laserTimer = null;
    laserTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) async {
      CanvasMapApiClass.api.getLaserData().then((value) {
        laserStream.add(value?.coordinates ?? []);
        getMapStream.add(true);
      });
    });
  }

  bool dragSelected = false;

  bool get allowMapDrag {
    if (drawVirtualWall && (!dragSelected)) {
      return false;
    }
    return true;
  }

///////// --------------------------- Virtual Wall Screen---------------------------------////////

  bool drawVirtualWall = false;

  VirtualWallPoint virtualWallPoint = VirtualWallPoint(pose: VirtualWallPose(point1: Point(x: 0, y: 0), point2: Point(x: 0, y: 0)));
  List<VirtualWallPoint> updatedVirtualWallPoints = [];
  bool point1Marked = false;

  void updateDrawVirtualWall(bool drawVirtualWall) async {
    point1Marked = false;
    eraseVirtualWall = false;
    updatedVirtualWallPoints.clear();
    this.drawVirtualWall = drawVirtualWall;
    if (!this.drawVirtualWall) {
      await saveVirtualWall();
      getVirtualWall();
    }
    notifyListeners();
  }

  void markPoint1(double x, double y) {
    virtualWallPoint.pose.point1.x = x;
    virtualWallPoint.pose.point1.y = y;
    virtualWallPoint.pose.point2.x = x;
    virtualWallPoint.pose.point2.y = y;
    currentMouseCursorPosition = [virtualWallPoint.pose.point1.x, virtualWallPoint.pose.point1.y];
    point1Marked = true;
    refreshCurrentMousePosition();
  }

  void markPoint2(double x, double y) {
    virtualWallPoint.pose.point2.x = x;
    virtualWallPoint.pose.point2.y = y;
    virtualWallPoint.pose.point1.x = virtualWallPoint.pose.point1.x.convertXToDasherPoint;
    virtualWallPoint.pose.point1.y = virtualWallPoint.pose.point1.y.convertYToDasherPoint;
    virtualWallPoint.pose.point2.x = virtualWallPoint.pose.point2.x.convertXToDasherPoint;
    virtualWallPoint.pose.point2.y = virtualWallPoint.pose.point2.y.convertYToDasherPoint;
    updatedVirtualWallPoints.add(virtualWallPoint);
    virtualWallPoint = VirtualWallPoint(pose: VirtualWallPose(point1: Point(x: 0, y: 0), point2: Point(x: 0, y: 0)));
    refreshDrawnVirtualWallPainter();
    refreshDrawingVirtualWallPainter();
    point1Marked = false;
  }

  bool eraseVirtualWall = false;

  EraseVirtualWallPoint? eraseVirtualWallPoint;

  void startUpdateErasedMarkPoint(double x, double y) {
    eraseVirtualWallPoint = EraseVirtualWallPoint(
      pose: EraseVirtualWallPose(
        point1: Point(x: 0, y: 0),
        point2: Point(x: 0, y: 0),
        point3: Point(x: 0, y: 0),
        point4: Point(x: 0, y: 0),
      ),
    );
    eraseVirtualWallPoint?.pose.point1.x = x;
    eraseVirtualWallPoint?.pose.point1.y = y;
    eraseVirtualWallPoint?.pose.point2.x = x;
    eraseVirtualWallPoint?.pose.point2.y = y;
    eraseVirtualWallPoint?.pose.point3.x = x;
    eraseVirtualWallPoint?.pose.point3.y = y;
    eraseVirtualWallPoint?.pose.point4.x = x;
    eraseVirtualWallPoint?.pose.point4.y = y;
    refreshErasedVirtualWallPainter();
  }

  void updateErasedMarkPoint(double x, double y) {
    eraseVirtualWallPoint?.pose.point2.x = x;
    eraseVirtualWallPoint?.pose.point3.x = x;
    eraseVirtualWallPoint?.pose.point3.y = y;
    eraseVirtualWallPoint?.pose.point4.y = y;
    refreshErasedVirtualWallPainter();
  }

  void endUpdateErasedMarkPoint() {
    final wallPoint1X = eraseVirtualWallPoint!.pose.point1.x.convertXToDasherPoint;
    final wallPoint1Y = eraseVirtualWallPoint!.pose.point1.y.convertYToDasherPoint;
    final wallPoint2X = eraseVirtualWallPoint!.pose.point2.x.convertXToDasherPoint;
    final wallPoint2Y = eraseVirtualWallPoint!.pose.point2.y.convertYToDasherPoint;
    final wallPoint3X = eraseVirtualWallPoint!.pose.point3.x.convertXToDasherPoint;
    final wallPoint3Y = eraseVirtualWallPoint!.pose.point3.y.convertYToDasherPoint;
    final wallPoint4X = eraseVirtualWallPoint!.pose.point4.x.convertXToDasherPoint;
    final wallPoint4Y = eraseVirtualWallPoint!.pose.point4.y.convertYToDasherPoint;
    List<VirtualWallPoint> virtualWallAffectedList = [];
    eraseVirtualWallPoint = null;
    for (var walls in virtualWall) {
      bool lineIntersect = lineIntersectsRectangle(
        Offset(wallPoint1X, wallPoint1Y),
        Offset(wallPoint2X, wallPoint2Y),
        Offset(wallPoint3X, wallPoint3Y),
        Offset(wallPoint4X, wallPoint4Y),
        Offset(walls.pose.point1.x, walls.pose.point1.y),
        Offset(walls.pose.point2.x, walls.pose.point2.y),
      );
      if (lineIntersect) {
        virtualWallAffectedList.add(walls);
      }
    }
    for (var walls in virtualWallAffectedList) {
      virtualWall.removeWhere((element) =>
          (element.pose.point1.x == walls.pose.point1.x) && (element.pose.point1.y == walls.pose.point1.y) && (element.pose.point2.x == walls.pose.point2.x) && (element.pose.point2.y == walls.pose.point2.y));
    }
    refreshVirtualWallPainter();
    refreshErasedVirtualWallPainter();
  }

  bool lineIntersectsRectangle(Offset rectPoint1, Offset rectPoint2, Offset rectPoint3, Offset rectPoint4, Offset lineStart, Offset lineEnd) {
    double minX = min(min(min(rectPoint1.dx, rectPoint2.dx), rectPoint3.dx), rectPoint4.dx);
    double maxX = max(max(max(rectPoint1.dx, rectPoint2.dx), rectPoint3.dx), rectPoint4.dx);
    double minY = min(min(min(rectPoint1.dy, rectPoint2.dy), rectPoint3.dy), rectPoint4.dy);
    double maxY = max(max(max(rectPoint1.dy, rectPoint2.dy), rectPoint3.dy), rectPoint4.dy);

    // Check if any endpoint of the line is inside the rectangle
    if ((lineStart.dx >= minX && lineStart.dx <= maxX && lineStart.dy >= minY && lineStart.dy <= maxY) || (lineEnd.dx >= minX && lineEnd.dx <= maxX && lineEnd.dy >= minY && lineEnd.dy <= maxY)) {
      return true;
    }

    // Check if any part of the line intersects with any part of the rectangle
    if (lineStart.dx < minX && lineEnd.dx < minX) {
      return false;
    }
    if (lineStart.dx > maxX && lineEnd.dx > maxX) {
      return false;
    }
    if (lineStart.dy < minY && lineEnd.dy < minY) {
      return false;
    }
    if (lineStart.dy > maxY && lineEnd.dy > maxY) {
      return false;
    }

    // Calculate line equation (y = mx + b)
    double m = (lineEnd.dy - lineStart.dy) / (lineEnd.dx - lineStart.dx);
    double b = lineStart.dy - m * lineStart.dx;

    // Check if any intersection point lies within the rectangle
    if (minX <= lineStart.dx && lineStart.dx <= maxX && minY <= m * minX + b && m * minX + b <= maxY) {
      return true;
    }
    if (minX <= lineEnd.dx && lineEnd.dx <= maxX && minY <= m * maxX + b && m * maxX + b <= maxY) {
      return true;
    }
    if (minY <= lineStart.dy && lineStart.dy <= maxY && minX <= (minY - b) / m && (minY - b) / m <= maxX) {
      return true;
    }
    if (minY <= lineEnd.dy && lineEnd.dy <= maxY && minX <= (maxY - b) / m && (maxY - b) / m <= maxX) {
      return true;
    }

    return false;
  }

  Future<void> updateEraseVirtualWall(bool eraseVirtualWall) async {
    this.eraseVirtualWall = eraseVirtualWall;
    eraseVirtualWallPoint = null;
    if (eraseVirtualWallPoint == null) {
      VirtualWallResponseModel? virtualWallModel = await CanvasMapApiClass.api.getVirtualWall();
      virtualWall = virtualWallModel?.waypoints ?? [];
      refreshVirtualWallPainter();
      refreshErasedVirtualWallPainter();
    }
    notifyListeners();
  }

  double scale = 1;
  Offset initialPosition = const Offset(0, 0);

  TransformationController transformationController = TransformationController();

  resetInitialPosition() {
    scale = 1;
    transformationController.value = Matrix4.identity()..translate((MapVariables.map.width / 2));
    //reDrawCanvas();
    notifyListeners();
  }

  ///---------Relocate----------------///
  bool isRelocationSelected = false;
  bool isRebuildMap = false;
  bool isIncrementMap = false;

  updateValueSelected({bool? isRelocationSelected, bool? isRebuildMap, bool? isIncrementMap}) {
    this.isRelocationSelected = isRelocationSelected ?? this.isRelocationSelected;
    this.isRebuildMap = isRebuildMap ?? this.isRebuildMap;
    this.isIncrementMap = isIncrementMap ?? this.isIncrementMap;
    relocationPoint = null;
    notifyListeners();
  }

  RelocationResponseModel? relocationPoint;

  void markRelocationStartPoint(double x, double y) {
    relocationPoint = null;
    relocationPoint = RelocationResponseModel(startPoint: RelocationPoint(x: x, y: y), endPoint: RelocationPoint(x: x, y: y), theta: 0);
    refreshRelocation();
  }

  void updateRelocationPoint(double x, double y) {
    relocationPoint?.endPoint.x = x;
    relocationPoint?.endPoint.y = y;
    if (relocationPoint != null) {
      relocationPoint!.theta = Offset(relocationPoint!.startPoint.x, relocationPoint!.startPoint.y).calculateTheta(Offset(relocationPoint!.endPoint.x, relocationPoint!.endPoint.y));
    }
    refreshRelocation();
  }

  void markRelocationEndPoint(BuildContext context) {
    if (relocationPoint != null) {
      relocationPoint!.theta = Offset(relocationPoint!.startPoint.x, relocationPoint!.startPoint.y).calculateTheta(Offset(relocationPoint!.endPoint.x, relocationPoint!.endPoint.y));

      ///Relocate Call
      robotEventManger.relocateByCoordinates(Pose(x: relocationPoint!.startPoint.x.convertXToDasherPoint, y: relocationPoint!.startPoint.y.convertYToDasherPoint, theta: relocationPoint!.theta).toJson());
    }
    relocationPoint = null;
    refreshRelocation();
  }

  //// ------------------------------- Navi Routes ----------------------------------------///

  void updateDrawRoute() async {
    newRoutePoints.clear();
    notifyListeners();
    //reDrawCanvas();
  }

  void addCreateRoutePoints(double x, double y) async {
    newRoutePoints.add([x, y]);
    //reDrawCanvas();
  }

  void updateCurrentCursorPosition(double x, double y) async {
    currentMouseCursorPosition = [x, y];
    refreshCurrentMousePosition();
  }

  void addFinalRoutePoint(double x, double y) async {
    newRoutePoints.add([x, y]);
    for (var routePoint in newRoutePoints) {
      routePoint.first = routePoint.first.convertXToDasherPoint;
      routePoint.last = routePoint.last.convertYToDasherPoint;
    }
  }

  List<double> currentMouseCursorPosition = [0, 0];

  List<List<double>> newRoutePoints = [];

  Waypoint? getPointAtPosition(double x, double y) {
    Waypoint? tappedPoint;
    for (var points in waypointsList) {
      if (((points.pose!.x!.convertXFromDasherPoint - x).abs() <= 5) && ((points.pose!.y!.convertYFromDasherPoint - y).abs() <= 5)) {
        tappedPoint = points;
      }
    }
    return tappedPoint;
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  void refreshMapPainter({bool isNotify = true}) {
    globalRef!.read(mapPainterController).refreshMapPainter(isNotify: isNotify);
  }

  void refreshWaypointsPainter({bool isNotify = true}) {
    globalRef!.read(waypointsPainterController).refreshWaypointsPainter(isNotify: isNotify);
  }

  void refreshPositionPainter({bool isNotify = true}) {
    globalRef!.read(positionPainterController).refreshPositionPainter(isNotify: isNotify);
  }

  void refreshVirtualWallPainter({bool isNotify = true}) {
    globalRef!.read(virtualWallPainterController).refreshVirtualWallPainter(isNotify: isNotify);
  }

  void refreshDrawingVirtualWallPainter({bool isNotify = true}) {
    globalRef!.read(drawingVirtualWallPainterController).refreshDrawingVirtualWallPainter(isNotify: isNotify);
  }

  void refreshDrawnVirtualWallPainter({bool isNotify = true}) {
    globalRef!.read(drawnVirtualWallPainterController).refreshDrawnVirtualWallPainter(isNotify: isNotify);
  }

  void refreshErasedVirtualWallPainter({bool isNotify = true}) {
    globalRef!.read(erasedVirtualWallPainterController).refreshErasedVirtualWallPainter(isNotify: isNotify);
  }

  void refreshCurrentMousePosition({bool isNotify = true}) {
    globalRef!.read(currentMousePositionPainterController).refreshCurrentMousePosition(isNotify: isNotify);
  }

  void refreshRelocation({bool isNotify = true}) {
    globalRef!.read(relocationPainterController).refreshRelocation(isNotify: isNotify);
  }

  void refreshContinousData({bool isNotify = true}) {
    globalRef!.read(continousDataPainterController).refreshContinousData(isNotify: isNotify);
  }

  void refreshRoutesPainter({bool isNotify = true}) {
    globalRef!.read(routesPainterController).refreshRoutesPainter(isNotify: isNotify);
  }

  void refreshEntireCanvas() {
    refreshVirtualWallPainter();
    refreshWaypointsPainter();
    refreshRoutesPainter();
    refreshPositionPainter();
  }
}
