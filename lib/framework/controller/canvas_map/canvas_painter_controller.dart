import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_controller.dart';
import 'package:odigo_offline/ui/canvas_map/helper/custom_painter_canvas.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';

final mapPainterController = ChangeNotifierProvider((ref) => MapPainterController());

class MapPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  MapPainterCanvas? mapPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter
  void refreshMapPainter({bool isNotify = true}) {
    if (canvasMapRead.image != null) {
      mapPainterCanvas = MapPainterCanvas(image: canvasMapRead.image!, scale: canvasMapRead.scale);
      if (isNotify) notifyListeners();
    }
  }
}

final positionPainterController = ChangeNotifierProvider((ref) => PositionPainterController());

class PositionPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  PositionPainterCanvas? positionPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshPositionPainter({bool isNotify = true}) {
    if (canvasMapRead.odigoImage != null) {
      positionPainterCanvas = PositionPainterCanvas(currentPosition: canvasMapRead.currentPosition, scale: canvasMapRead.scale, odigoImage: canvasMapRead.odigoImage);
      if (isNotify) notifyListeners();
    }
  }
}

final waypointsPainterController = ChangeNotifierProvider((ref) => WaypointsPainterController());

class WaypointsPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  WaypointsPainterCanvas? waypointsPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshWaypointsPainter({bool isNotify = true}) {
    waypointsPainterCanvas = WaypointsPainterCanvas(
        chargingPointImage: canvasMapRead.chargingPointImage,
        productionPointImage: canvasMapRead.productionPointImage,
        deliveryPointImage: canvasMapRead.deliveryPointImage,
        wayPoints: canvasMapRead.waypointsList,
        scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final virtualWallPainterController = ChangeNotifierProvider((ref) => VirtualWallPainterController());

class VirtualWallPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  VirtualWallPainterCanvas? virtualWallPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshVirtualWallPainter({bool isNotify = true}) {
    virtualWallPainterCanvas = VirtualWallPainterCanvas(virtualWalls: canvasMapRead.virtualWall, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final drawingVirtualWallPainterController = ChangeNotifierProvider((ref) => DrawingVirtualWallPainterController());

class DrawingVirtualWallPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  DrawingVirtualWallPainterCanvas? drawingVirtualWallPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshDrawingVirtualWallPainter({bool isNotify = true}) {
    drawingVirtualWallPainterCanvas = DrawingVirtualWallPainterCanvas(currentlyDrawingVirtualWall: canvasMapRead.virtualWallPoint, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final drawnVirtualWallPainterController = ChangeNotifierProvider((ref) => DrawnVirtualWallPainterController());

class DrawnVirtualWallPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  DrawnVirtualWallPainterCanvas? drawnVirtualWallPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshDrawnVirtualWallPainter({bool isNotify = true}) {
    drawnVirtualWallPainterCanvas = DrawnVirtualWallPainterCanvas(drawnVirtualWalls: canvasMapRead.updatedVirtualWallPoints, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final erasedVirtualWallPainterController = ChangeNotifierProvider((ref) => ErasedVirtualWallPainterController());

class ErasedVirtualWallPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  ErasedVirtualWallPainterCanvas? erasedVirtualWallPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter
  void refreshErasedVirtualWallPainter({bool isNotify = true}) {
    erasedVirtualWallPainterCanvas = ErasedVirtualWallPainterCanvas(erasedVirtualWallData: canvasMapRead.eraseVirtualWallPoint, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final currentMousePositionPainterController = ChangeNotifierProvider((ref) => CurrentMousePositionPainterController());

class CurrentMousePositionPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  CurrentMousePositionPainter? currentMousePositionPainter;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshCurrentMousePosition({bool isNotify = true}) {
    currentMousePositionPainter = CurrentMousePositionPainter(currentMouseCursorPosition: canvasMapRead.currentMouseCursorPosition, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final relocationPainterController = ChangeNotifierProvider((ref) => RelocationPainterController());

class RelocationPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  RelocationPainter? relocationPainter;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshRelocation({bool isNotify = true}) {
    relocationPainter = RelocationPainter(relocationPoint: canvasMapRead.relocationPoint, scale: canvasMapRead.scale, odigoImage: canvasMapRead.odigoImage);
    if (isNotify) notifyListeners();
  }
}

final continousDataPainterController = ChangeNotifierProvider((ref) => ContinousDataPainterController());

class ContinousDataPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  ContinousDataCanvas? continousDataCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter-

  void refreshContinousData({bool isNotify = true}) {
    continousDataCanvas = ContinousDataCanvas(laserData: canvasMapRead.laserData, threeDData: canvasMapRead.threeDData, globalPath: canvasMapRead.globalPath, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}

final routesPainterController = ChangeNotifierProvider((ref) => RoutesPainterController());

class RoutesPainterController extends ChangeNotifier {
  ///CANVAS PAINTER
  RoutesPainterCanvas? routesPainterCanvas;

  get canvasMapRead => globalRef!.read(canvasMapController);

  ///Refresh Canvas Painter

  void refreshRoutesPainter({bool isNotify = true}) {
    routesPainterCanvas = RoutesPainterCanvas(routeImage: canvasMapRead.routeImage, naviRoutes: canvasMapRead.naviRoutes, scale: canvasMapRead.scale);
    if (isNotify) notifyListeners();
  }
}
