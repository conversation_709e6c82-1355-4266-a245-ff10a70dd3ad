import 'dart:async';
import 'package:flutter/services.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_constant.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_offline/framework/controller/wifi/available_wifi_controller.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/repository/wifi/wifi_model.dart';
import 'package:odigo_offline/framework/utils/helper/voice_helper.dart';

import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/theme/app_assets.dart';
import 'package:odigo_offline/ui/utils/theme/assets.gen.dart';

/// Manages robot actions and listens for events from the native side through a method channel.
class RobotEventManger {
  // Singleton instance getter for RobotActionManger
  static RobotEventManger get instance {
    robotActionManger ??= RobotEventManger._();
    return robotActionManger!;
  }

  static RobotEventManger? robotActionManger;

  /// Method channel used for communication with the native side.
  static const MethodChannel _channel = MethodChannel(RobotActionConstant.methodChannelName);

  /// Private constructor to initialize the method call handler.
  RobotEventManger._() {
    getAndroidWifi();
    // RobotMetaDataRepository.instance.hostIpAddress = null;
    RobotMetaDataRepository.instance.hostIpAddress = '*************';
    RobotMetaDataRepository.instance.hostWifiName = 'KODYWIFI';
    CanvasMapApiClass.api.initDio(ipAddress: RobotMetaDataRepository.instance.hostIpAddress);
    getHostName();
    getHostIp();
    getCoreData();
    _channel.setMethodCallHandler(
      (MethodCall call) {
        Map<dynamic, dynamic> args = call.arguments!;
        switch (call.method) {
          case RobotActionConstant.onHostnameEvent:
            hostNameEvent = HostNameEvent(rawData: args['rawData'], hostName: args['hostName']);
            hostNameStream.add(hostNameEvent!);
            break;
          case RobotActionConstant.onCoreDataEvent:
            coreDataEvent = CoreDataEvent(
              rawData: args['rawData'],
              bumper: args['bumper'],
              button: args['button'],
              cliff: args['cliff'],
              charger: args['charger'],
              battery: args['battery'],
            );
            coreDataStream.add(coreDataEvent!);
            break;
          case RobotActionConstant.onIpEvent:
            ipEvent = IpEvent(
              rawData: args['rawData'],
              wifiName: args['wifiName'],
              ipAddress: args['ipAddress'],
            );
            RobotMetaDataRepository.instance.hostIpAddress = ipEvent?.ipAddress;
            RobotMetaDataRepository.instance.hostWifiName = ipEvent?.wifiName;
            final wifiWatch = globalRef?.read(availableWifiController);
            wifiWatch?.rosConnectedWifi = WifiModel(deviceName: ipEvent?.wifiName, ipAddress: ipEvent?.ipAddress);
            wifiWatch?.notifyListeners();
            CanvasMapApiClass.api.initDio(ipAddress: ipEvent?.ipAddress);
            ipStream.add(ipEvent!);
            break;
          case RobotActionConstant.onHflsVersionEvent:
            hflsVersionEvent = HflsVersionEvent(
              rawData: args['rawData'],
              softVersion: args['softVersion'],
              loaderVersion: args['loaderVersion'],
              firmwareVersion: args['firmwareVersion'],
              hardwareVersion: args['hardwareVersion'],
            );
            hflsVersionStream.add(hflsVersionEvent!);
            break;
          case RobotActionConstant.onPositionEvent:
            positionEvent = PositionEvent(
              rawData: args['rawData'],
              x: args['x'],
              y: args['y'],
              theta: args['theta'],
              refresh: args['refresh'],
            );
            positionStream.add(positionEvent!);
            break;
          case RobotActionConstant.onSensorEvent:
            sensorEvent = SensorEvent(
              rawData: args['rawData'],
              imuWarn: args['imuWarn'],
              imuWarnCount: args['imuWarnCount'],
              lidarWarn: args['lidarWarn'],
              lidarWarnCount: args['lidarWarnCount'],
              odomWarn: args['odomWarn'],
              odomWarnCount: args['odomWarnCount'],
              cam3DWarn: args['cam3DWarn'],
              cam3DWarnCount: args['cam3DWarnCount'],
            );
            sensorStream.add(sensorEvent!);
            break;
          case RobotActionConstant.onLaserEvent:
            laserEvent = LaserEvent(
              rawData: args['rawData'],
              laser: args['laser'],
            );
            laserStream.add(laserEvent!);
            break;
          case RobotActionConstant.onWiFiEvent:
            wifiEvent = WifiEvent(
              isConnect: args['isConnect'],
            );
            wifiStream.add(wifiEvent!);
            break;
          case RobotActionConstant.onVersionEvent:
            versionEvent = VersionEvent(
              rawData: args['rawData'],
              version: args['version'],
            );
            versionStream.add(versionEvent!);
            break;
          case RobotActionConstant.onApplyMapEvent:
            applyMapEvent = ApplyMapEvent(
              map: args['map'],
              alias: args['alias'],
              success: args['success'],
            );
            applyMapStream.add(applyMapEvent!);
            break;
          case RobotActionConstant.onMapEvent:
            mapEvent = MapEvent(
              map: args['map'],
              alias: args['alias'],
            );
            mapStream.add(mapEvent!);
            break;
          case RobotActionConstant.onNavModeEvent:
            navModeEvent = NavModeEvent(
              mode: args['mode'],
            );
            navModeStream.add(navModeEvent!);
            break;
          case RobotActionConstant.onNaviResult:
            navResultEvent = NavResultEvent(
              state: args['state'],
              code: args['code'],
              name: args['name'],
              distToGoal: args['distToGoal'],
              mileage: args['mileage'],
              isNavigating: args['isNavigating'],
            );
            navResultStream.add(navResultEvent!);
            break;
          case RobotActionConstant.onSetFlagPointEvent:
            setFlagPointEvent = SetFlagPointEvent(
              result: args['result'],
            );
            setFlagPointStream.add(setFlagPointEvent!);
            break;
          case RobotActionConstant.onDelFlagPointEvent:
            delFlagPointEvent = DelFlagPointEvent(
              result: args['result'],
            );
            delFlagPointStream.add(delFlagPointEvent!);
            break;
          case RobotActionConstant.onEncounterObstacleEvent:
            obstacleEncounterEvent = ObstacleEncounterEvent(
              type: args['type'],
            );
            obstacleEncounterStream.add(obstacleEncounterEvent!);
            print(obstacleEncounterEvent);
            VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceObstacle);
            break;
        }
        return Future.value();
      },
    );
  }

  // Events and their respective listeners
  HostNameEvent? hostNameEvent;
  CoreDataEvent? coreDataEvent;
  IpEvent? ipEvent;
  IpEvent? androidIpEvent;
  HflsVersionEvent? hflsVersionEvent;
  PositionEvent? positionEvent;
  SensorEvent? sensorEvent;
  LaserEvent? laserEvent;
  WifiEvent? wifiEvent;
  VersionEvent? versionEvent;
  ApplyMapEvent? applyMapEvent;
  MapEvent? mapEvent;
  NavResultEvent? navResultEvent;
  NavModeEvent? navModeEvent;
  SetFlagPointEvent? setFlagPointEvent;
  DelFlagPointEvent? delFlagPointEvent;
  ObstacleEncounterEvent? obstacleEncounterEvent;

  // Callback listeners for different events
  StreamController<HostNameEvent> hostNameStream = StreamController<HostNameEvent>.broadcast();
  StreamController<CoreDataEvent> coreDataStream = StreamController<CoreDataEvent>.broadcast();
  StreamController<IpEvent> ipStream = StreamController<IpEvent>.broadcast();
  StreamController<IpEvent> androidIpStream = StreamController<IpEvent>.broadcast();
  StreamController<HflsVersionEvent> hflsVersionStream = StreamController<HflsVersionEvent>.broadcast();
  StreamController<PositionEvent> positionStream = StreamController<PositionEvent>.broadcast();
  StreamController<SensorEvent> sensorStream = StreamController<SensorEvent>.broadcast();
  StreamController<LaserEvent> laserStream = StreamController<LaserEvent>.broadcast();
  StreamController<WifiEvent> wifiStream = StreamController<WifiEvent>.broadcast();
  StreamController<VersionEvent> versionStream = StreamController<VersionEvent>.broadcast();
  StreamController<ApplyMapEvent> applyMapStream = StreamController<ApplyMapEvent>.broadcast();
  StreamController<MapEvent> mapStream = StreamController<MapEvent>.broadcast();
  StreamController<NavModeEvent> navModeStream = StreamController<NavModeEvent>.broadcast();
  StreamController<SetFlagPointEvent> setFlagPointStream = StreamController<SetFlagPointEvent>.broadcast();
  StreamController<DelFlagPointEvent> delFlagPointStream = StreamController<DelFlagPointEvent>.broadcast();
  StreamController<NavResultEvent> navResultStream = StreamController<NavResultEvent>.broadcast();
  StreamController<ObstacleEncounterEvent> obstacleEncounterStream = StreamController<ObstacleEncounterEvent>.broadcast();

  // Dispose functions for each stream
  /// Closes the `hostNameStream` to clean up resources.
  void disposeHostNameStream() {
    hostNameStream.close();
  }

  /// Closes the `coreDataStream` to clean up resources.
  void disposeCoreDataStream() {
    coreDataStream.close();
  }

  /// Closes the `ipStream` to clean up resources.
  void disposeIpStream() {
    ipStream.close();
  }

  /// Closes the `androidIpStream` to clean up resources.
  void disposeAndroidIpStream() {
    androidIpStream.close();
  }

  /// Closes the `hflsVersionStream` to clean up resources.
  void disposeHflsVersionStream() {
    hflsVersionStream.close();
  }

  /// Closes the `positionStream` to clean up resources.
  void disposePositionStream() {
    positionStream.close();
  }

  /// Closes the `sensorStream` to clean up resources.
  void disposeSensorStream() {
    sensorStream.close();
  }

  /// Closes the `laserStream` to clean up resources.
  void disposeLaserStream() {
    laserStream.close();
  }

  /// Closes the `wifiStream` to clean up resources.
  void disposeWifiStream() {
    wifiStream.close();
  }

  /// Closes the `versionStream` to clean up resources.
  void disposeVersionStream() {
    versionStream.close();
  }

  /// Closes the `applyMapStream` to clean up resources.
  void disposeApplyMapStream() {
    applyMapStream.close();
  }

  /// Closes the `mapStream` to clean up resources.
  void disposeMapStream() {
    mapStream.close();
  }

  /// Closes the `mapStream` to clean up resources.
  void disposeMapModeStream() {
    mapStream.close();
  }

  /// Closes the `setFlagPointStream` to clean up resources.
  void disposeSetFlagPointStream() {
    setFlagPointStream.close();
  }

  /// Closes the `delFlagPointStream` to clean up resources.
  void disposeDelFlagPointStream() {
    delFlagPointStream.close();
  }

  // General cleanup method for disposing all streams
  /// Closes all streams to ensure proper resource management.
  void disposeAllStreams() {
    disposeHostNameStream();
    disposeCoreDataStream();
    disposeIpStream();
    disposeAndroidIpStream();
    disposeHflsVersionStream();
    disposePositionStream();
    disposeSensorStream();
    disposeLaserStream();
    disposeWifiStream();
    disposeVersionStream();
    disposeApplyMapStream();
    disposeMapStream();
    disposeMapModeStream();
    disposeSetFlagPointStream();
    disposeDelFlagPointStream();
  }

  /// Methods to invoke native actions

  /// Fetches the hostname from the robot.
  Future<void> getHostName() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.getHostName);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Fetches the IP address from the robot.
  Future<void> getHostIp() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.getHostIp);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Gets the robot's current position.
  Future<void> getPosition() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.getPosition);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Commands the robot to move forward.
  Future<void> moveForward() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.moveForward);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Commands the robot to move backward.
  Future<void> moveBackward() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.moveBackward);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Commands the robot to move left.
  Future<void> moveLeft() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.moveLeft);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Commands the robot to move right.
  Future<void> moveRight() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.moveRight);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Shuts down the robot.
  Future<void> shutdown() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.shutdown);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Reboots down the robot.
  Future<void> powerReboot() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.powerReboot);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Get current map name.
  Future<void> getCurrentMap() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.getCurrentMap);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Apply Map.
  Future<void> applyMap(String mapName) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.applyMap, {'map': mapName});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Set Navi Mode.
  Future<void> naviMode() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.setNaviMode);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Set Build Mode.
  Future<void> setRebuildMode() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.setRebuildMode);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Set Incremental Mode.
  Future<void> setIncrementalMode() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.setIncrementalMode);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Get Mode.
  Future<void> getMode() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.getMode);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Start Navigation.
  Future<bool> startNavigation(String pointName) async {
    try {
      setNavigationSpeed('${RobotMetaDataRepository.instance.navigationSpeed}');
      return await _channel.invokeMethod<bool>(RobotActionConstant.startNavigation, {'pointName': pointName}) ?? false;
    } catch (e) {
      showLog(e.toString());
      return false;
    }
  }

  /// Pause Navigation.
  Future<bool> pauseNavigation() async {
    try {
      return await _channel.invokeMethod<bool>(RobotActionConstant.pauseNavigation) ?? false;
    } catch (e) {
      showLog(e.toString());
      return false;
    }
  }

  /// Resume Navigation.
  Future<bool> resumeNavigation() async {
    try {
      return await _channel.invokeMethod<bool>(RobotActionConstant.resumeNavigation) ?? false;
    } catch (e) {
      showLog(e.toString());
      return false;
    }
  }

  /// Cancel Navigation.
  Future<bool> cancelNavigation() async {
    try {
      return await _channel.invokeMethod<bool>(RobotActionConstant.cancelNavigation) ?? false;
    } catch (e) {
      showLog(e.toString());
      return false;
    }
  }

  /// Get Core Data.
  Future<bool> getCoreData() async {
    try {
      return await _channel.invokeMethod<bool>(RobotActionConstant.getCoreData) ?? false;
    } catch (e) {
      showLog(e.toString());
      return false;
    }
  }

  /// Set Navigation Speed.
  Future<void> setNavigationSpeed(String navigationSpeed) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.setNavigationSpeed, {'navigationSpeed': navigationSpeed});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Start Route Navigation.
  Future<void> startRouteNavigation(String pointName, List<List<double>> pointsList) async {
    try {
      setNavigationSpeed('${RobotMetaDataRepository.instance.cruiseSpeed}');
      await _channel.invokeMethod(RobotActionConstant.startRouteNavigation, {'pointName': pointName, 'pointsList': pointsList});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Relocate By Point Name.
  Future<void> relocateByPointName(String point) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.relocateByPointName, {'point': point});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Relocate by Coordinates.
  Future<void> relocateByCoordinates(Map data) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.relocateByCoordinates, data);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Set Point.
  Future<void> setPoint({required double x, required double y, required double theta, required String pointName, required String pointType}) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.setPoint, {'x': x, 'y': y, 'theta': theta, 'pointName': pointName, 'pointType': pointType});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Delete Point.
  Future<void> deletePoint(String pointName) async {
    try {
      await _channel.invokeMethod(RobotActionConstant.deletePoint, {'pointName': pointName});
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Save Map.
  Future<void> saveMap() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.saveMap);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Turn On All Lights.
  Future<void> turnOnAllLights() async {
    try {
      await _channel.invokeMethod(RobotActionConstant.turnOnAllLights);
    } catch (e) {
      showLog(e.toString());
    }
  }

  /// Fetches the Android Wi-Fi information.
  Future<void> getAndroidWifi() async {
    try {
      var map = await const MethodChannel(odigoAndroidChannel).invokeMethod(RobotActionConstant.getAndroidWifi);
      androidIpEvent = IpEvent(
        rawData: '',
        wifiName: map['wifiName'],
        ipAddress: map['ipAddress'],
      );
      RobotMetaDataRepository.instance.androidWifiName = androidIpEvent!.wifiName;
      RobotMetaDataRepository.instance.androidIpAddress = androidIpEvent!.ipAddress;
    } catch (e) {
      showLog(e.toString());
    }
  }
}
