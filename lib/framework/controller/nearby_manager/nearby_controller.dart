import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/controller/asset_master/asset_master_controller.dart';
import 'package:odigo_offline/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_offline/framework/controller/delivery/delivery_controller.dart';
import 'package:odigo_offline/framework/controller/locations/locations_controller.dart';
import 'package:odigo_offline/framework/controller/map/map_controller.dart';
import 'package:odigo_offline/framework/controller/progress_controller/progress_controller.dart';
import 'package:odigo_offline/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_offline/framework/controller/route/route_controller.dart';
import 'package:odigo_offline/framework/controller/splash/splash_controller.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/repository/asset/contract/select_media_repository.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:odigo_offline/framework/utils/encryption/encryption_extension.dart';
import 'package:odigo_offline/framework/utils/helper/nearby/nearby_constants.dart';
import 'package:odigo_offline/framework/utils/helper/nearby/nearby_manager.dart';
import 'package:odigo_offline/framework/utils/helper/nearby/nearby_objects.dart';
import 'package:odigo_offline/framework/utils/helper/objectbox_provider.dart';
import 'package:odigo_offline/framework/utils/helper/unzip_utils.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';

import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as p;

final nearbyManagementController = ChangeNotifierProvider((ref) => getIt<NearbyManagementController>());

@injectable
class NearbyManagementController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;
    if (isNotify) {
      notifyListeners();
    }
  }

  SelectMediaRepository selectMediaRepository;

  NearbyManagementController(this.selectMediaRepository);

  final mainServiceChannel = const MethodChannel(odigoAndroidChannel);

  String androidID = '';

  Future<void> getAndroidId() async {
    androidID = await mainServiceChannel.invokeMethod('getAndroidId');
    RobotMetaDataRepository.instance.androidId = androidID;
    notifyListeners();
  }

  NearbyManager nearbyManager = NearbyManager();

  /// this will be displayed in QR
  String encodedWifiQrData = '';

  bool popDialogAfterConnect = true;
  bool allowInteraction = true;

  /// QR dialog Key
  GlobalKey wifiQrDialogKey = GlobalKey(debugLabel: 'wifiQrDialogKey');

  bool isShowQrButtonLoading = false;

  void updateIsShowQrButtonLoading(bool value) {
    isShowQrButtonLoading = value;
    notifyListeners();
  }

  /// Encrypts the data that will be displayed in QR and stores it in [encodedWifiQrData]
  void encryptWifiQrData(BuildContext context, {bool isBackup = false}) {
    String wifiQrData = jsonEncode(
      {
        QrParameters.qrType.name: QrType.addAsset.name,
        QrParameters.androidId.name: RobotMetaDataRepository.instance.sessionData?.androidId,
        QrParameters.isBackup.name: isBackup,
      },
    );
    encodedWifiQrData = wifiQrData.encryptString;
  }

  void resetNearbyProgress() {
    receivedFileType = null;
    receivedFilePath = null;
    receivedFileName = null;
    isLastStep = false;
    currentTransferType = null;
    receivedBytes = null;
    totalBytes = null;
    notifyListeners();
  }

  bool showQrButton = true;

  PayloadType? currentTransferType;
  String? receivedFilePath;
  String? receivedFileName;
  String? receivedFileType;
  int? receivedFileLength;
  bool isLastStep = false;
  int? receivedBytes;
  int? totalBytes;
  Uuid uuid = const Uuid();
  String? connectedRemoteId;

  double get progress => ((receivedBytes ?? 0) / (totalBytes ?? 1));

  Future<void> startAdvertising(BuildContext context) async {
    await nearbyManager.stopAdvertising();
    await nearbyManager.startAdvertising(
      packageName,
      Strategy.P2P_POINT_TO_POINT,
      onConnectionInitiated: (endpointId, connectionInfo) {
        nearbyManager.acceptConnection(
          endpointId,
          onPayLoadReceived: (String endpointId, Payload payload) {
            currentTransferType = payload.type;
            if (payload.filePath?.isNotEmpty ?? false) {
              receivedFilePath = payload.filePath;
            }
            if (payload.bytes != null) {
              String fileTransferString = String.fromCharCodes(payload.bytes!);
              handleBytesReceivedData(context, fileTransferString, endpointId);
            }
          },
        );
      },
      onConnectionResult: (endpointId, status) async {
        if (status == Status.CONNECTED) {
          connectedRemoteId = endpointId;
          allowInteraction = true;
          showQrButton = false;
          notifyListeners();
          globalRef?.read(locationsController).showCreateLocationButton = true;
          if (wifiQrDialogKey.currentContext != null && popDialogAfterConnect) {
            Navigator.pop(wifiQrDialogKey.currentContext!);
          }
        }
      },
      onDisconnected: (endpointId) {
        showQrButton = true;
        connectedRemoteId = null;
        allowInteraction = true;
        globalRef?.read(locationsController).showCreateLocationButton = true;
        if (backupReceivedDialogShown && context.mounted) Navigator.pop(context);
        resetNearbyProgress();
        nearbyManager.disposePayloadReceivedTransferUpdate();
        showDisplayScreenSnackbar(context, title: 'Connection Lost');
        startAdvertising(context);
      },
      serviceId: RobotMetaDataRepository.instance.sessionData?.androidId ?? '',
    );
  }

  List<FileSystemEntity> queueFileList = [];

  /// Returns list of all the files store in advertisement
  Future<List<FileSystemEntity>> getAdvertisementFileList(String advertisementName) async {
    Directory tempDirectory = Directory('');
    tempDirectory = Directory(advertisementName);
    if (!((await tempDirectory.exists()))) {
      await tempDirectory.create();
    }
    queueFileList = tempDirectory.listSync();
    notifyListeners();
    return queueFileList;
  }

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  CancelToken? mapDownloadCancelToken;
  bool backupReceivedDialogShown = false;

  void handleBytesReceivedData(BuildContext context, String fileTransferString, String endpointId) async {
    var method = jsonDecode(fileTransferString)['method'];
    switch (method) {
      case 'setOdigoName':
        RobotMetaDataRepository.instance.odigoName = jsonDecode(fileTransferString)['data']['odigoName'];
        notifyListeners();
        break;
      case 'receiveObjectBoxFromOdigo':
        startObjectBoxFileTransfer(context, endpointId, fileTransferString);
        break;
      case 'receiveThumbnailsFromOdigo':
        startThumbnailFileTransfer(context, endpointId, fileTransferString);
        break;
      case 'receiveAllDataFromOdigo':
        nearbyManager.sendBytesPayload(endpointId, await globalRef!.read(assetMasterController).allSendToRemoteData);
        break;
      case 'deleteAsset':
        onAssetDeleteCall(globalRef!, jsonDecode(fileTransferString)['data']['assetId']);
      case 'manageShowLocationCreateButton':
        final locationWatch = globalRef!.read(locationsController);
        locationWatch.showCreateLocationButton = jsonDecode(fileTransferString)['data'];
      case 'addUpdateLocation':
        var data = jsonDecode(fileTransferString)['data'];
        final locationWatch = globalRef!.read(locationsController);
        locationWatch.setLocationModel = LocationModel.fromJson(data);
        await locationWatch.addUpdateLocation();
        break;
      case 'deleteLocation':
        var uuid = jsonDecode(fileTransferString)['data'] as String;
        final locationWatch = globalRef!.read(locationsController);
        await locationWatch.deleteLocation(locationUuid: uuid);
        break;
      case 'fileTransferStart':
        allowInteraction = false;
        receivedFileName = jsonDecode(fileTransferString)['data']['fileName'];
        receivedFileType = jsonDecode(fileTransferString)['data']['fileType'];
        globalRef?.read(assetMasterController).updateAssetType(assetTypeValues.map[receivedFileType] ?? AssetType.video);
        Future.delayed(Durations.medium2, () {
          globalRef?.read(assetMasterController).animateToBottom();
        });
        totalBytes = jsonDecode(fileTransferString)['fileSize'];
        nearbyManager.listenToPayloadReceivedTransferUpdate(onPayloadTransferUpdate: (endpointId, payloadTransferUpdate) {
          handleOnFileReceivedTransferUpdate(context, endpointId, payloadTransferUpdate);
        });
        notifyListeners();
        break;
      case 'backUpData':
        backUpData(context, endpointId);
        break;
      case 'backUpDataStep':
        backUpDataStep(
          context,
          endpointId,
          jsonDecode(fileTransferString)['data']['mediaType'],
          (jsonDecode(fileTransferString)['data']['assetList'] as List).map((e) => e.toString()).toList(),
        );
        break;
      case 'restoreBackup':
        backupReceivedDialogShown = true;
        if (jsonDecode(fileTransferString)['data']['isFirstStep']) {
          var dir = Directory('$applicationDirectoryPath/Zip');
          if (dir.existsSync()) {
            dir.delete(recursive: true);
          }
        }
        showCommonSwitchingDialog(context: context, text: 'Receiving Backup from Remote', onDialogPop: () => backupReceivedDialogShown = false, showProgress: true);
        receivedFileName = jsonDecode(fileTransferString)['data']['fileName'];
        receivedFileType = jsonDecode(fileTransferString)['data']['fileType'];
        receivedFileLength = jsonDecode(fileTransferString)['data']['fileLength'];
        isLastStep = jsonDecode(fileTransferString)['data']['isLastStep'];
        nearbyManager.listenToPayloadReceivedTransferUpdate(onPayloadTransferUpdate: (endpointId, payloadTransferUpdate) {
          handleOnFileReceivedTransferUpdate(context, endpointId, payloadTransferUpdate);
        });
        notifyListeners();
        break;
      case 'cancelBackupData':
        if (mapDownloadCancelToken != null) {
          mapDownloadCancelToken?.cancel();
          mapDownloadCancelToken = null;
          if (context.mounted) Navigator.pop(context);
        }
        break;
    }
  }

  void onAssetDeleteCall(WidgetRef ref, String assetUuid) async {
    final assetMasterWatch = ref.read(assetMasterController);
    final nearbyManagementWatch = ref.read(nearbyManagementController);
    assetMasterWatch.selectMediaRepository.deleteAssetFromUuid(assetUuid);
    assetMasterWatch.getAssetList();
    await ref.read(splashController).getStorage();
    if (!nearbyManagementWatch.showQrButton && nearbyManagementWatch.connectedRemoteId != null) {
      nearbyManager.sendBytesPayload(
        nearbyManagementWatch.connectedRemoteId!,
        Uint8List.fromList(jsonEncode(
          {
            'method': 'deleteAssetSuccess',
            'data': {
              'assetUuid': assetUuid,
              'androidId': RobotMetaDataRepository.instance.androidId,
              'availableStorage': RobotMetaDataRepository.instance.availableStorage,
              'usedStorage': RobotMetaDataRepository.instance.usedStorage,
              'totalStorage': RobotMetaDataRepository.instance.totalStorage,
            }
          },
        ).codeUnits),
      );
    }
  }

  void handleOnFileReceivedTransferUpdate(BuildContext context, String endpointId, PayloadTransferUpdate payloadTransferUpdate) async {
    receivedBytes = payloadTransferUpdate.bytesTransferred;
    totalBytes = payloadTransferUpdate.totalBytes;
    globalRef?.read(progressController).updateProgress(progress * 100);
    notifyListeners();
    if (payloadTransferUpdate.status == PayloadStatus.SUCCESS && receivedFileType != null) {
      onTransferUpdate(context, endpointId);
    } else if (payloadTransferUpdate.status == PayloadStatus.CANCELED) {
      if (backupReceivedDialogShown && context.mounted) Navigator.pop(context);
      resetNearbyProgress();
      nearbyManager.disposePayloadReceivedTransferUpdate();
      allowInteraction = true;
      showDisplayScreenSnackbar(context, title: 'Transfer Cancelled');
    }
  }

  void onTransferUpdate(BuildContext context, String endpointId) async {
    if (currentTransferType != null) {
      if (currentTransferType == PayloadType.FILE) {
        nearbyManager.disposePayloadReceivedTransferUpdate();
        Directory mainDirectory = applicationDirectory;
        mainDirectory = Directory('${mainDirectory.path}/Zip');
        if (!mainDirectory.existsSync()) await mainDirectory.create();
        final ReceivePort resultPort = ReceivePort();
        final receivedFile = File(receivedFilePath ?? '');
        globalRef?.read(progressController).updateProgress(0);
        if (backupReceivedDialogShown && context.mounted) Navigator.pop(context);
        if (receivedFileType == 'BACKUP') {
          await FileUtils.instance.moveFilesToDirectory([receivedFile], mainDirectory);
          if (isLastStep && mainDirectory.listSync().length >= (receivedFileLength ?? 4)) {
            String sendFileByte = jsonEncode({'method': 'restoringBackup'});
            nearbyManager.sendBytesPayload(endpointId, Uint8List.fromList(sendFileByte.codeUnits));
            if (context.mounted) restoreBackup(context, endpointId, mainDirectory.path);
          }
          return;
        }
        if (context.mounted) showCommonSwitchingDialog(context: context, text: 'Unzipping File', showProgress: true);
        await Isolate.spawn(FileUtils.instance.unzipFile, IsolateUnzipModel(receivedFilePath ?? '', mainDirectory.path, resultPort.sendPort));
        resultPort.listen((message) async {
          if (jsonDecode(message)['type'] == 'progress') {
            globalRef?.read(progressController).updateProgress(jsonDecode(message)['progress']);
          }
          if (jsonDecode(message)['type'] == 'success') {
            resultPort.close();
            if (context.mounted) Navigator.pop(context);
            if (receivedFile.existsSync()) {
              await receivedFile.delete();
            }
            final fileList = await getAdvertisementFileList(mainDirectory.path);
            List<FileReceivedModel> newFileList = [];
            for (final file in fileList) {
              String uuidStr = uuid.v4();
              Directory directory;
              directory = Directory('${mainDirectory.path.replaceAll('/Zip', '')}/Assets/$receivedFileType/$uuidStr');
              if (!directory.existsSync()) {
                await directory.create(recursive: true);
              }
              File newFile = File('${directory.path}/${file.name}');
              File receivedFile = File(file.path);
              await newFile.create();
              if (receivedFile.existsSync()) {
                await receivedFile.copy(newFile.path);
                await receivedFile.delete();
              }
              receivedFilePath = newFile.path;
              FileReceivedModel fileReceivedModel = FileReceivedModel(uuid: uuidStr, fileName: newFile.name, filePath: newFile.path, fileType: receivedFileType ?? '');
              newFileList.add(fileReceivedModel);
            }
            allowInteraction = true;
            if (context.mounted) globalRef?.read(assetMasterController).onAllFilesReceived(context, endpointId, newFileList);
            currentTransferType = null;
          }
        });
      }
    }
  }

  List<String> fileList = [];
  List<String> relativePaths = [];

  Future<void> backUpData(BuildContext context, String endpointId) async {
    RobotMetaDataRepository.instance.isBackupRestored = true;
    Directory directory = applicationDirectory;
    String? objectBoxPath = directory.listSync().where((file) => file.name == ObjectBoxProvider.objectBox.objectBoxProvider).firstOrNull?.path;
    if (objectBoxPath != null && context.mounted) {
      showCommonSwitchingDialog(context: context, text: 'Downloading Map', showProgress: true);
      Directory objectBoxDirectory = Directory(objectBoxPath);
      final receivePort = ReceivePort('receivePort');
      List<String> fileList = objectBoxDirectory.listSync().map((file) => file.path).toList();
      List<String> relativePaths = objectBoxDirectory.listSync().map((file) => file.path.split('/').last).toList();
      String? mapPath;
      try {
        mapPath = await CanvasMapApiClass.api.exportMap(
          RobotMetaDataRepository.instance.currentMap!,
          objectBoxDirectory.path,
          onReceiveProgress: (progress, total) {
            globalRef?.read(progressController).updateProgress((progress / total) * 100);
          },
          onCancelTokenCreated: (cancelToken) => mapDownloadCancelToken = cancelToken,
        );
      } catch (e) {
        globalRef?.read(progressController).updateProgress(0);
        mapDownloadCancelToken = null;
        showLog(e.toString());
      } finally {
        if (mapPath != null) {
          mapDownloadCancelToken = null;
          fileList.add(mapPath);
          relativePaths.add(mapPath.split('/').last);
          globalRef?.read(progressController).updateProgressText('Zipping Files');
          String sendFileByte = jsonEncode({'method': 'mapDownloaded'});
          nearbyManager.sendBytesPayload(endpointId, Uint8List.fromList(sendFileByte.codeUnits));
          await Isolate.spawn(
            FileUtils.instance.zipFiles,
            IsolateZipModel(fileList, relativePaths, '${DateTime.now().toIso8601String()}.zip', receivePort.sendPort, directory.path),
          );
          receivePort.listen((message) async {
            if (jsonDecode(message)['type'] == 'progress') {
              globalRef?.read(progressController).updateProgress(jsonDecode(message)['progress']);
            } else if (jsonDecode(message)['type'] == 'success') {
              var zipFilePath = await jsonDecode(message)['path'];
              receivePort.close();
              RobotMetaDataRepository.instance.isBackupRestored = false;
              String sendFileByte = jsonEncode({
                'method': 'backupStart',
                'data': {
                  "fileName": File(zipFilePath).name,
                  "fileSize": File(zipFilePath).lengthSync(),
                  "mapName": RobotMetaDataRepository.instance.currentMap,
                  "robotAndroidId": RobotMetaDataRepository.instance.androidId,
                  "uuid": const Uuid().v4(),
                },
              });
              globalRef?.read(progressController).updateProgressText('Sending File');
              if (context.mounted) sendFileBytesHandler(context, endpointId, zipFilePath, mapFilePath: mapPath, sendFileByte: sendFileByte);
            }
          });
        }
      }
    }
  }

  Future<void> backUpDataStep(BuildContext context, String endpointId, String mediaType, List<String> assetList) async {
    Directory directory = applicationDirectory;
    final receivePort = ReceivePort('receivePort');
    List<String> fileList = [];
    List<String> relativePaths = [];
    showCommonSwitchingDialog(context: context, text: 'Zipping Files', showProgress: true);
    String sendFileByte = jsonEncode({'method': 'mapDownloaded'});
    nearbyManager.sendBytesPayload(endpointId, Uint8List.fromList(sendFileByte.codeUnits));
    Directory assetDirectory = applicationDirectory;
    assetDirectory = Directory('${assetDirectory.path}/Assets/$mediaType');
    void collectFiles(Directory dir, String basePath) {
      for (var entity in dir.listSync(recursive: true)) {
        if (entity is File) {
          List<String> pathList = entity.path.split('/');
          pathList.removeLast();
          if (assetList.contains(pathList.last)) {
            fileList.add(entity.path);
            relativePaths.add(p.relative(entity.path, from: basePath));
          }
        }
      }
    }

    collectFiles(assetDirectory, assetDirectory.path.replaceAll('/$mediaType', ''));
    await Isolate.spawn(
      FileUtils.instance.zipFiles,
      IsolateZipModel(fileList, relativePaths, '${DateTime.now().toIso8601String()}.zip', receivePort.sendPort, directory.path),
    );
    receivePort.listen((message) async {
      if (jsonDecode(message)['type'] == 'progress') {
        globalRef?.read(progressController).updateProgress(jsonDecode(message)['progress']);
      } else if (jsonDecode(message)['type'] == 'success') {
        var zipFilePath = await jsonDecode(message)['path'];
        receivePort.close();
        RobotMetaDataRepository.instance.isBackupRestored = false;
        String sendFileByte = jsonEncode({
          'method': 'backupStartStep',
          'data': {
            "fileName": '$mediaType.zip',
            "fileSize": File(zipFilePath).lengthSync(),
            "robotAndroidId": RobotMetaDataRepository.instance.androidId,
          },
        });
        globalRef?.read(progressController).updateProgressText('Sending File');
        if (context.mounted) sendFileBytesHandler(context, endpointId, zipFilePath, sendFileByte: sendFileByte);
      }
    });
  }

  Future<void> restoreBackup(BuildContext context, String endpointId, String zipFilePath) async {
    if (RobotMetaDataRepository.instance.hostWifiName != RobotMetaDataRepository.instance.androidWifiName) {
      nearbyManager.sendBytesPayload(endpointId, Uint8List.fromList(jsonEncode({'method': 'fileReceived', 'type': 'media'}).codeUnits));
      if (context.mounted) Navigator.pop(context);
      if (context.mounted) {
        showMessageDialog(
          context,
          message: 'Please make sure that ROS and Android are connected to same wifi network',
          didDismiss: () {
            globalRef?.read(navigationStackController).push(const NavigationStackItem.availableWiFi(dpPop: true));
          },
        );
      }
      return;
    }
    Directory assetDirectory = Directory("$applicationDirectoryPath/Assets");
    ReceivePort resultPort = ReceivePort();
    if (assetDirectory.existsSync()) {
      await assetDirectory.delete(recursive: true);
    }
    if (context.mounted) showCommonSwitchingDialog(context: context, text: 'Unzipping File', showProgress: true);
    for (var file in Directory(zipFilePath).listSync()) {
      await Isolate.spawn(FileUtils.instance.unzipFileAsync, IsolateUnzipModel(file.path, '$applicationDirectoryPath/Unzipped', resultPort.sendPort));
      await resultPort.first;
      resultPort.close();
      resultPort = ReceivePort();
    }
    if (Directory(zipFilePath).existsSync()) {
      await Directory(zipFilePath).delete(recursive: true);
    }
    await FileUtils.instance.moveDirectory('${'$applicationDirectoryPath/Unzipped'}/IMAGE', '$applicationDirectoryPath/Assets/IMAGE');
    await FileUtils.instance.moveDirectory('${'$applicationDirectoryPath/Unzipped'}/VIDEO', '$applicationDirectoryPath/Assets/VIDEO');
    await FileUtils.instance.moveDirectory('${'$applicationDirectoryPath/Unzipped'}/AUDIO', '$applicationDirectoryPath/Assets/AUDIO');
    notifyListeners();
    var fileList = Directory('$applicationDirectoryPath/Unzipped').listSync();
    List<FileSystemEntity> dataFileList = fileList.where((file) => !file.path.contains('.png')).toList();
    FileSystemEntity? mapFile = fileList.where((file) => file.path.contains('.png')).firstOrNull;
    if (mapFile == null) {
      if (context.mounted) showMessageDialog(context, message: 'There is some issue with the backup and cannot be restored');
      return;
    }
    Directory directory = applicationDirectory;
    String? objectBoxPath = directory.listSync().where((file) => file.name == ObjectBoxProvider.objectBox.objectBoxProvider).firstOrNull?.path;
    if (objectBoxPath != null) {
      Directory objectboxDirectory = Directory(objectBoxPath);
      await FileUtils.instance.deleteInsideDirectory(objectboxDirectory);
      await FileUtils.instance.moveFilesToDirectory(dataFileList.map((e) => File(e.path)).toList(), objectboxDirectory);
      if (context.mounted) showCommonSwitchingDialog(context: context, text: 'Uploading Map', showProgress: true);
      await CanvasMapApiClass.api.uploadNewMapApi(mapFile.path, onSendProgress: (progress, total) {
        globalRef?.read(progressController).updateProgress((progress / total) * 100);
      });
      if (context.mounted) Navigator.pop(context);
      String? mapName = (await CanvasMapApiClass.api.getCurrentMapName())?.name;
      if (mapFile.name.split('.png').first != mapName) {
        if (context.mounted) {
          showCommonSwitchingDialog(context: context, text: 'Applying map please wait');
          globalRef?.read(mapController).applyMapEvent(context, globalRef!, mapName: mapFile.name.split('.png').first, afterMapApply: () async {
            await globalRef?.read(mapController).ipMapListDataApi(context);
            globalRef?.read(locationsController).locationRepository.setupNewMap();
            if (context.mounted) globalRef?.read(deliveryController).getDeliveryPointsApi(context);
            if (context.mounted) globalRef?.read(deliveryController).getRouteList(context);
            globalRef?.read(routeController).getNaviRoutes();
            RobotMetaDataRepository.instance.currentMap = mapFile.name.split('.png').first;
            if (context.mounted) showBackupSuccessHandler(context, endpointId);
          });
        }
      } else {
        if (context.mounted) await globalRef?.read(mapController).ipMapListDataApi(context);
        globalRef?.read(locationsController).locationRepository.setupNewMap();
        if (context.mounted) globalRef?.read(deliveryController).getDeliveryPointsApi(context);
        if (context.mounted) globalRef?.read(deliveryController).getRouteList(context);
        globalRef?.read(routeController).getNaviRoutes();
        RobotMetaDataRepository.instance.currentMap = mapFile.name.split('.png').first;
        if (context.mounted) showBackupSuccessHandler(context, endpointId);
      }
      await FileUtils.instance.deleteInsideDirectory(Directory(zipFilePath));
    }
  }

  showBackupSuccessHandler(BuildContext context, String endpointId) {
    if (context.mounted) {
      showMessageDialog(context, message: 'Backup Complete, your app will be restarted to load all data', didDismiss: () {
        nearbyManager.sendBytesPayload(endpointId, Uint8List.fromList(jsonEncode({'method': 'fileReceived', 'type': 'media'}).codeUnits));
        if (kReleaseMode) {
          nearbyManager.disconnectFromEndpoint(endpointId);
          globalRef?.read(splashController).restartApp();
        }
      });
    }
  }

  void sendFileBytesHandler(BuildContext context, String? endpointId, String zipFilePath, {String? mapFilePath, String? sendFileByte}) {
    if (sendFileByte != null) {
      nearbyManager.sendBytesPayload(endpointId ?? '', Uint8List.fromList(sendFileByte.codeUnits));
      nearbyManager.listenToPayloadSentTransferUpdate(
        onPayloadTransferUpdate: (endpointId, payloadTransferUpdate) {
          if (payloadTransferUpdate.status == PayloadStatus.SUCCESS) {
            sendFilePayload(context, endpointId, zipFilePath, mapFilePath: mapFilePath);
          }
        },
      );
    } else {
      sendFilePayload(context, endpointId!, zipFilePath, mapFilePath: mapFilePath);
    }
  }

  void sendFilePayload(BuildContext context, String endpointId, String zipFilePath, {String? mapFilePath}) {
    nearbyManager.sendFilePayload(endpointId, zipFilePath);
    nearbyManager.listenToPayloadSentTransferUpdate(
      onPayloadTransferUpdate: (endpointId, payloadTransferUpdate) async {
        globalRef?.read(progressController).updateProgress((payloadTransferUpdate.bytesTransferred / payloadTransferUpdate.totalBytes) * 100);
        if (payloadTransferUpdate.status == PayloadStatus.SUCCESS) {
          await File(zipFilePath).delete(recursive: true);
          if (mapFilePath != null) {
            await File(mapFilePath).delete(recursive: true);
          }
          if (context.mounted) Navigator.pop(context);
          nearbyManager.disposePayloadSentTransferUpdate();
        }
      },
    );
  }

  void startObjectBoxFileTransfer(BuildContext context, String endpointId, String fileTransferString) async {
    Directory directory = applicationDirectory;
    String? objectBoxPath = directory.listSync().where((file) => file.name == ObjectBoxProvider.objectBox.objectBoxProvider).firstOrNull?.path;
    if (objectBoxPath != null && context.mounted) {
      showCommonSwitchingDialog(context: context, text: 'Syncing Data', showProgress: true);
      Directory objectBoxDirectory = Directory(objectBoxPath);
      List<String> fileList = objectBoxDirectory.listSync().map((e) => e.path).toList();
      print(fileList.map((e) => File(e).statSync().size).toList());
      final receivePort = ReceivePort('receivePort');
      await Isolate.spawn(
        FileUtils.instance.zipFiles,
        IsolateZipModel(fileList, fileList.map((e) => e.split('/odigo-tv').last).toList(), '${DateTime.now().toIso8601String()}.zip', receivePort.sendPort, applicationDirectoryPath),
      );
      receivePort.listen((message) async {
        if (jsonDecode(message)['type'] == 'progress') {
          globalRef?.read(progressController).updateProgress(jsonDecode(message)['progress']);
        } else if (jsonDecode(message)['type'] == 'success') {
          var zipFilePath = await jsonDecode(message)['path'];
          if (context.mounted) sendFileBytesHandler(context, endpointId, zipFilePath);
          receivePort.close();
        }
      });
    }
  }

  void startThumbnailFileTransfer(BuildContext context, String endpointId, String fileTransferString) async {
    if (context.mounted) {
      showCommonSwitchingDialog(context: context, text: 'Syncing Data', showProgress: true);
      // List<Asset> assetList = selectMediaRepository.fetchAssetFromUuidList((jsonDecode(fileTransferString)['data'] as List).map((e) => e.toString()).toList());
      List<Asset> assetList = selectMediaRepository.getAssetListFromListAssetType(assetType: [AssetType.image, AssetType.video]);
      List<String> fileList = assetList.map((e) => e.assetThumbnail!).toList();
      final receivePort = ReceivePort('receivePort');
      await Isolate.spawn(
        FileUtils.instance.zipFiles,
        IsolateZipModel(fileList, fileList.map((e) => e.split('/app_flutter').last).toList(), '${DateTime.now().toIso8601String()}.zip', receivePort.sendPort, applicationDirectoryPath),
      );
      receivePort.listen((message) async {
        if (jsonDecode(message)['type'] == 'progress') {
          globalRef?.read(progressController).updateProgress(jsonDecode(message)['progress']);
        } else if (jsonDecode(message)['type'] == 'success') {
          var zipFilePath = await jsonDecode(message)['path'];
          if (context.mounted) sendFileBytesHandler(context, endpointId, zipFilePath);
          receivePort.close();
        }
      });
    }
  }
}

class FileReceivedModel {
  String uuid;
  String fileName;
  String filePath;
  String fileType;

  FileReceivedModel({
    required this.uuid,
    required this.fileName,
    required this.filePath,
    required this.fileType,
  });
}

extension FileSystemEntityExtension on FileSystemEntity {
  String get name {
    return p.basename(this.path);
  }
}
