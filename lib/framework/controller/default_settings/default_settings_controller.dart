import 'dart:developer';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/repository/default_settings/model/default_dashboard_media_model.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/locations/contract/location_repository.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/framework/repository/default_settings/contract/default_setting_repository.dart';
import 'package:odigo_offline/framework/repository/default_settings/model/default_setting_model.dart';
import 'package:odigo_offline/framework/utils/ui_state.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:odigo_offline/framework/repository/asset/model/response/asset_model.dart';
import 'package:volume_controller/volume_controller.dart';

final defaultSettingsController = ChangeNotifierProvider((ref) => getIt<DefaultSettingsController>());

@injectable
class DefaultSettingsController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    /// Get locations
    selectedDefaultSettingsTabIndex = 0;
    selectedMediaIndex = 0;
    if (isNotify) {
      notifyListeners();
    }
  }

  /// Default Settings tab list
  List<String> defaultSettingsTabList = [
    LocaleKeys.keyMovingTo,
    LocaleKeys.keyReachedTo,
  ];

  List<String> mediaType = [
    LocaleKeys.keyVideo,
    LocaleKeys.keyImage,
    LocaleKeys.keyAudio,
  ];

  ///Default Dashboard Media
  List<String> dashboardMediaType = [LocaleKeys.keyVideo, LocaleKeys.keyImage];
  DefaultDashboardMediaModel? dashboardMediaModel;

  int selectedDefaultSettingsTabIndex = 0;
  int selectedMediaIndex = 0;

  ///Change DefaultSettings tab
  void changeDefaultSettingsTabIndex(index) {
    selectedDefaultSettingsTabIndex = index;
    notifyListeners();
  }

  ///Change Media Type
  void updateSelectedMediaIndex(index) {
    selectedMediaIndex = index;
    notifyListeners();
  }

  GlobalKey playVideoDialogKey = GlobalKey();
  DefaultSettingModel? defaultSettingModel;

  LocationModel? productionPoint;
  LocationModel? chargingPoint;

  /// Play audio

  /// Play audio method
  final audioPlayer = AudioPlayer();

  void playAudio({required String assetPath}) async {
    audioPlayer.stop();
    await audioPlayer.setSourceDeviceFile(assetPath);
    await audioPlayer.play(audioPlayer.source!);
    notifyListeners();
  }

  void pauseAudio() {
    audioPlayer.stop();
    notifyListeners();
  }

  void updateDefaultMovingToReachedSelectedAssetType({required AssetType assetType, required DefaultSettingType settingType}) {
    defaultSettingRepository.updateSettingSelectedMediaType(defaultSettingType: settingType, assetType: assetType);
    getDefaultSettings();
    notifyListeners();
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  /// Get default settings
  Future<void> getDefaultSettings() async {
    defaultSettingModel = await defaultSettingRepository.getDefaultSettings();
    notifyListeners();
  }

  Future<void> getDefaultDashboardMediaSettings() async {
    dashboardMediaModel = await defaultSettingRepository.getDefaultDashboardMediaSettings();
    notifyListeners();
  }

  /// Update default settings
  Future<void> updateDefaultDashboardMediaSettings(context, {required Asset? asset}) async {
    defaultSettingState.isLoading = true;
    defaultSettingState.success = null;
    await getDefaultDashboardMediaSettings();
    notifyListeners();
    dashboardMediaModel?.asset.target = asset;
    dashboardMediaModel = await defaultSettingRepository.saveDefaultDashboardMediaSettings(dashboardMediaModel);
    await Future.delayed(const Duration(milliseconds: 500));
    notifyListeners();
  }

  DefaultSettingRepository defaultSettingRepository;
  LocationRepository locationRepository;

  DefaultSettingsController(this.defaultSettingRepository, this.locationRepository);

  var defaultSettingState = UIState<DefaultSettingModel>();

  /// Update default settings
  Future<void> updateDefaultSettings(context, {required DefaultSettingType defaultSettingType, required AssetType assetType, required Asset? asset}) async {
    defaultSettingState.isLoading = true;
    defaultSettingState.success = null;
    await getDefaultSettings();
    notifyListeners();
    switch (defaultSettingType) {
      case DefaultSettingType.general:
      case DefaultSettingType.onWay:
        switch (assetType) {
          case AssetType.video:
            defaultSettingModel?.defaultOnWayAsset.target?.video.target = asset;
            break;
          case AssetType.image:
            defaultSettingModel?.defaultOnWayAsset.target?.image.target = asset;
            break;
          case AssetType.audio:
            defaultSettingModel?.defaultOnWayAsset.target?.audio.target = asset;
            break;
        }
        break;
      case DefaultSettingType.reached:
        switch (assetType) {
          case AssetType.video:
            defaultSettingModel?.defaultReachedAsset.target?.video.target = asset;
            break;
          case AssetType.image:
            defaultSettingModel?.defaultReachedAsset.target?.image.target = asset;
            break;
          case AssetType.audio:
            defaultSettingModel?.defaultReachedAsset.target?.audio.target = asset;
            break;
        }
        break;
    }
    log('${defaultSettingModel?.toJson()}');
    defaultSettingModel = await defaultSettingRepository.saveDefaultSettings(defaultSettingModel);
    await Future.delayed(const Duration(milliseconds: 500));
    notifyListeners();
  }

  initControllerSettings() {
    volume = RobotMetaDataRepository.instance.systemVolume;
    cruiseSpeed = RobotMetaDataRepository.instance.cruiseSpeed;
    navigationSpeed = RobotMetaDataRepository.instance.navigationSpeed;
    navigationWaitingTime = RobotMetaDataRepository.instance.navigationWaitingTime;
    robotStopWaitingTime = RobotMetaDataRepository.instance.robotStopWaitingTime;
    imageSliderTime = RobotMetaDataRepository.instance.imageSliderTime;
    showLocationAssetInCruise = RobotMetaDataRepository.instance.showLocationAssetInCruise;
    showVideoInEmergency = RobotMetaDataRepository.instance.showVideoInEmergency;
    defaultMode = RobotMetaDataRepository.instance.defaultMode;
    enableHsbcMode = RobotMetaDataRepository.instance.enableHsbcMode;
    hsbcStopWaitingTime = RobotMetaDataRepository.instance.hsbcStopWaitingTime;
    VolumeController().setVolume(volume, showSystemUI: false);
    notifyListeners();
  }

  ///Volume
  double volume = 0.1;

  updateVolume({double? volume}) {
    if ((volume ?? 0.1) < 0.1) {
      this.volume = 0.1;
    } else {
      this.volume = volume ?? 0.1;
    }
    VolumeController().setVolume(this.volume, showSystemUI: false);
    RobotMetaDataRepository.instance.systemVolume = this.volume;
    notifyListeners();
  }

  ///Speed
  double cruiseSpeed = 0.3;

  updateCruiseSpeed({double? cruiseSpeed}) {
    if ((cruiseSpeed ?? 0.1) < 0.1) {
      this.cruiseSpeed = 0.1;
    } else {
      this.cruiseSpeed = cruiseSpeed ?? 0.1;
    }
    RobotMetaDataRepository.instance.cruiseSpeed = this.cruiseSpeed;
    notifyListeners();
  }

  ///Speed
  double navigationSpeed = 0.3;

  updateNavigationSpeed({double? navigationSpeed}) {
    if ((navigationSpeed ?? 0.1) < 0.1) {
      this.navigationSpeed = 0.1;
    } else {
      this.navigationSpeed = navigationSpeed ?? 0.1;
    }
    RobotMetaDataRepository.instance.navigationSpeed = this.navigationSpeed;
    notifyListeners();
  }

  ///time
  int navigationWaitingTime = 15;

  updateNavigationWaitingTime({int? navigationWaitingTime}) {
    if ((navigationWaitingTime ?? 8) < 8) {
      this.navigationWaitingTime = 8;
    } else {
      this.navigationWaitingTime = navigationWaitingTime ?? 8;
    }
    RobotMetaDataRepository.instance.navigationWaitingTime = this.navigationWaitingTime;
    notifyListeners();
  }

  ///time
  int robotStopWaitingTime = 15;

  updateRobotStopWaitingTime({int? robotStopWaitingTime}) {
    if ((robotStopWaitingTime ?? 8) < 8) {
      this.robotStopWaitingTime = 8;
    } else {
      this.robotStopWaitingTime = robotStopWaitingTime ?? 8;
    }
    RobotMetaDataRepository.instance.robotStopWaitingTime = this.robotStopWaitingTime;
    notifyListeners();
  }

  ///time
  int imageSliderTime = 5;

  updateImageSliderTime({int? imageSliderTime}) {
    if ((imageSliderTime ?? 5) < 5) {
      this.imageSliderTime = 5;
    } else {
      this.imageSliderTime = imageSliderTime ?? 5;
    }
    RobotMetaDataRepository.instance.imageSliderTime = this.imageSliderTime;
    notifyListeners();
  }

  ///showLocationAssetInCruise
  bool showLocationAssetInCruise = false;

  updateShowLocationAssetInCruise({bool? showLocationAssetInCruise}) {
    this.showLocationAssetInCruise = showLocationAssetInCruise ?? false;
    RobotMetaDataRepository.instance.showLocationAssetInCruise = this.showLocationAssetInCruise;
    notifyListeners();
  }

  ///showVideoInEmergency
  bool showVideoInEmergency = false;

  updateShowVideoInEmergency({bool? showVideoInEmergency}) {
    this.showVideoInEmergency = showVideoInEmergency ?? false;
    RobotMetaDataRepository.instance.showVideoInEmergency = this.showVideoInEmergency;
    notifyListeners();
  }

  ///showVideoInEmergency
  bool enableHsbcMode = false;

  updateEnableHsbcMode({bool? enableHsbcMode}) {
    this.enableHsbcMode = enableHsbcMode ?? false;
    RobotMetaDataRepository.instance.enableHsbcMode = this.enableHsbcMode;
    notifyListeners();
  }

  ///time
  int hsbcStopWaitingTime = 45;

  updateHsbcStopWaitingTime({int? hsbcStopWaitingTime}) {
    if ((hsbcStopWaitingTime ?? 15) < 15) {
      this.hsbcStopWaitingTime = 15;
    } else {
      this.hsbcStopWaitingTime = hsbcStopWaitingTime ?? 15;
    }
    RobotMetaDataRepository.instance.hsbcStopWaitingTime = this.hsbcStopWaitingTime;
    notifyListeners();
  }

  ///defaultMode
  String defaultMode = DisplayMode.CRUISE.name;
  List<String> defaultModeList = [DisplayMode.CRUISE.name, DisplayMode.NAVIGATION.name, DisplayMode.TOUR.name];

  updateDefaultMode({required String defaultMode}) {
    this.defaultMode = defaultMode;
    RobotMetaDataRepository.instance.defaultMode = this.defaultMode;
    notifyListeners();
  }
}
