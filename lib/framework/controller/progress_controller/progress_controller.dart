import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';

final progressController = ChangeNotifierProvider((ref) => getIt<ProgressController>());

@injectable
class ProgressController extends ChangeNotifier {
  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  double progress = 0.0;
  String progressText = '';

  updateProgressText(String progressText) {
    this.progressText = progressText;
    notifyListeners();
  }

  updateProgress(double progress) {
    this.progress = progress;
    notifyListeners();
  }
}
