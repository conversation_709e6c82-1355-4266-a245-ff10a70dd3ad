import 'package:odigo_offline/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_offline/framework/controller/hardware_controller/hardware_controller.dart';
import 'package:odigo_offline/framework/repository/locations/model/location_model.dart';
import 'package:odigo_offline/framework/dependency_injection/inject.dart';
import 'package:odigo_offline/framework/repository/locations/contract/location_repository.dart';
import 'package:odigo_offline/framework/repository/robot_meta_data/repository/robot_meta_data_api_repository.dart';
import 'package:odigo_offline/framework/repository/asset/contract/select_media_repository.dart';
import 'package:odigo_offline/framework/repository/tour/contract/tour_data_repository.dart';
import 'package:odigo_offline/framework/repository/tour/model/tour_locations_model.dart';
import 'package:odigo_offline/framework/repository/tour/model/tour_model.dart';
import 'package:odigo_offline/framework/utils/helper/voice_helper.dart';
import 'package:odigo_offline/ui/routing/delegate.dart';
import 'package:odigo_offline/ui/routing/navigation_stack_item.dart';
import 'package:odigo_offline/ui/routing/stack.dart';
import 'package:odigo_offline/ui/utils/const/app_enums.dart';
import 'package:odigo_offline/ui/utils/const/form_validations.dart';
import 'package:odigo_offline/ui/utils/theme/app_assets.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_offline/ui/utils/widgets/dialogs/common_dialogs.dart';
import 'package:uuid/uuid.dart';

final createTourController = ChangeNotifierProvider((ref) => getIt<CreateTourController>());

@injectable
class CreateTourController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;
    tourNameCtr.clear();
    tourLocationList = [];
    isChargeAdded = false;
    isHomeAdded = false;
    isProductionAdded = false;
    Future.delayed(const Duration(milliseconds: 100), () {
      formKey.currentState?.reset();
    });
    if (isNotify) {
      notifyListeners();
    }
  }

  clearFieldData() {
    tourNameCtr.clear();
    tourLocationList = [];
    isHomeAdded = false;
    isChargeAdded = false;
    Future.delayed(const Duration(milliseconds: 100), () {
      formKey.currentState?.reset();
    });
    notifyListeners();
  }

  ///form key and controller
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  TextEditingController tourNameCtr = TextEditingController();
  FocusNode focusNode = FocusNode();

  ///To check if all fields are valid or not
  bool isAllFieldsValid = false;

  ///Check Validity of Password
  void checkIfAllFieldsValid() {
    isAllFieldsValid = validateText(tourNameCtr.text, LocaleKeys.keyTourNameIsRequired.localized) == null;
    notifyListeners();
  }

  ///database repository
  TourDataRepository tourRepository;
  SelectMediaRepository mediaRepository;
  LocationRepository locationRepository;

  CreateTourController(this.tourRepository, this.mediaRepository, this.locationRepository);

  TourModel? tour;

  addDataToTour() {
    tour = TourModel();
    String uuid = const Uuid().v4();
    tour?.tourName = tourNameCtr.text;
    tour?.tourUuid = uuid;
    for (int i = 0; i < tourLocationList.length; i++) {
      tour?.locationMapping.add(TourLocationsModel());
      tour?.locationMapping[i].locationName = tourLocationList[i].locationName;
      tour?.locationMapping[i].locationPoint = tourLocationList[i].locationPoint.target?.name;
      tour?.locationMapping[i].tourUuid = uuid;
      tour?.locationMapping[i].locationUuid = tourLocationList[i].locationUuid;

      if (tour?.locationMapping[i].onWayAsset.target == null) {
        tour?.locationMapping[i].onWayAsset.target = tourLocationList[i].onWayAsset.target;
      }
      if (tour?.locationMapping[i].reachedAsset.target == null) {
        tour?.locationMapping[i].reachedAsset.target = tourLocationList[i].reachedAsset.target;
      }
    }
    clearFieldData();
    notifyListeners();
  }

  ///get tour data from database
  List<TourModel> tourList = [];

  getTourList() async {
    tourList.clear();
    List<TourModel> tours = tourRepository.getTourList();
    tourList.addAll(tours);
    notifyListeners();
  }

  ///add tour data to database
  addTour() async {
    await tourRepository.setTourData(tour);
    tour = TourModel();
    getTourList();
    notifyListeners();
  }

  ///delete tour list from database
  clearTourList() {
    tourRepository.clearAllTours();
    getTourList();
    notifyListeners();
  }

  deleteTour(int id) {
    tourRepository.deleteOneTour(id);
    getTourList();
    notifyListeners();
  }

  removeLocationFromTour(int tourIndex, int locationIndex) async {
    tourList[tourIndex].locationMapping.removeAt(locationIndex);

    notifyListeners();
  }

  updateTour(int tourIndex) async {
    await tourRepository.updateTourData(tourList[tourIndex]);
    getTourList();
    notifyListeners();
  }

  ///get all location list
  List<LocationModel> locationsList = [];
  List<TourLocationsModel> tourLocList = [];

  loadLocationList() async {
    locationsList.clear();
    List<LocationModel> locations = locationRepository.getLocationList();
    locationsList.addAll(locations);
    notifyListeners();
  }

  loadingTourLocationList() {
    tourLocList.clear();
    // List<TourLocationsModel> tourLoc = await tourRepository.getTourList();
  }

  ///select location
  LocationModel? selectedLocation;

  selectLocation(LocationModel value) {
    selectedLocation = value;
    notifyListeners();
  }

  ///add selected location to tour list
  List<LocationModel> tourLocationList = [];

  addLocationForEdit(List<LocationModel> locationList) {
    tourLocationList = [];
    tourLocationList.addAll(locationList);
    notifyListeners();
  }

  addLocationToTourList(LocationModel value) {
    List<LocationModel>? production = locationsList.where((element) => element.locationPoint.target?.type == LocationPointType.production.name).toList();
    bool isAlreadyProductionAdded = tourLocationList.firstOrNull?.locationPoint.target?.type == LocationPointType.production.name;
    if (isAlreadyProductionAdded) {
      if (production.contains(value)) {
        if (tourLocationList.firstOrNull?.locationUuid != value.locationUuid) {
          tourLocationList.removeAt(0);
          tourLocationList.insert(0, value);
        }
      }
    } else {
      tourLocationList.insert(0, production.first);
    }
    if (!tourLocationList.contains(value)) {
      tourLocationList.add(value);
      selectedLocation = null;
    }
    notifyListeners();
  }

  bool isHomeAdded = false;
  bool isChargeAdded = false;

  bool isProductionAdded = false;

  checkForProduction() {
    LocationModel? production = locationsList.where((element) => element.locationPoint.target?.type == LocationPointType.production.name).firstOrNull;
    if (production != null) {
      isProductionAdded = true;
    }
    notifyListeners();
  }

  ///check if home and charging point is added or not
  checkHome() {
    if (tourLocationList.firstOrNull?.locationPoint.target?.type == LocationPointType.production.name) {
      isHomeAdded = true;
    }
    notifyListeners();
  }

  ///----------function for temporary store audio , image , video list

  removeLocationFromList(LocationModel value) {
    if (tourLocationList.contains(value)) {
      tourLocationList.remove(value);
    }
    notifyListeners();
  }

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  ViewType selectedViewType = ViewType.expanded;

  updateSelectedViewType(ViewType selectedViewType) {
    this.selectedViewType = selectedViewType;
    notifyListeners();
  }

  startTour(BuildContext context, WidgetRef ref, TourModel tour) {
    showConfirmationDialog(
      context,
      'Start Tour',
      message: 'Are you sure to start this tour?',
      didTakeAction: (isPositive) async {
        if (isPositive) {
          final displayAdsScreenWatch = ref.read(displayAdsScreenController);

          if (RobotMetaDataRepository.instance.isEmergencyPressed) {
            showMessageDialog(
              context,
              message: 'Please Remove emergency first.',
              isError: true,
            );
            VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceEmergencyPressed);
            return;
          }
          if (!checkIfTourHasAssets(ref, tour)) {
            showMessageDialog(
              context,
              message: 'Please make sure all the location in the tour has required assets or add default assets in Media Settings',
            );
            return;
          }
          RobotMetaDataRepository.instance.defaultMode = DisplayMode.TOUR.name;
          await Future.delayed(const Duration(milliseconds: 500), () {
            ref.read(navigationStackController).popUntil(const NavigationStackItem.displayAds());
            displayAdsScreenWatch.startTour(tour: tour);
          });
        }
      },
    );
  }

  bool checkIfTourHasAssets(WidgetRef ref, TourModel tour) {
    bool movingToAssetPresent = isMovingToTourAssetPresent(tour) || isMovingToDefaultAssetPresent(ref);
    bool reachedAssetPresent = isReachedTourAssetPresent(tour) || isReachedDefaultAssetPresent(ref);
    return movingToAssetPresent && reachedAssetPresent;
  }

  bool isMovingToTourAssetPresent(TourModel tour) {
    var videoList = [];
    var imageList = [];
    var audioList = [];
    for (var locationInTour in tour.locationMapping) {
      videoList = locationInTour.onWayAsset.target?.video ?? [];
      audioList = locationInTour.onWayAsset.target?.audio ?? [];
      imageList = locationInTour.onWayAsset.target?.image ?? [];
      if (videoList.isEmpty && (imageList.isEmpty || audioList.isEmpty)) return false;
    }
    return true;
  }

  bool isReachedTourAssetPresent(TourModel tour) {
    var videoList = [];
    var imageList = [];
    var audioList = [];
    for (var locationInTour in tour.locationMapping) {
      videoList = locationInTour.reachedAsset.target?.video ?? [];
      audioList = locationInTour.reachedAsset.target?.audio ?? [];
      imageList = locationInTour.reachedAsset.target?.image ?? [];
      if (videoList.isEmpty && (imageList.isEmpty || audioList.isEmpty)) return false;
    }
    return true;
  }

  bool isMovingToDefaultAssetPresent(WidgetRef ref) {
    final displayAdsScreenWatch = ref.read(displayAdsScreenController);
    displayAdsScreenWatch.getDefaultSettingsSync();
    var video = displayAdsScreenWatch.defaultSettingModel?.defaultOnWayAsset.target?.video.target;
    var audio = displayAdsScreenWatch.defaultSettingModel?.defaultOnWayAsset.target?.audio.target;
    var image = displayAdsScreenWatch.defaultSettingModel?.defaultOnWayAsset.target?.image.target;
    if (video == null && (image == null || audio == null)) return false;
    return true;
  }

  bool isReachedDefaultAssetPresent(WidgetRef ref) {
    final displayAdsScreenWatch = ref.read(displayAdsScreenController);
    displayAdsScreenWatch.getDefaultSettingsSync();
    var video = displayAdsScreenWatch.defaultSettingModel?.defaultReachedAsset.target?.video.target;
    var audio = displayAdsScreenWatch.defaultSettingModel?.defaultReachedAsset.target?.audio.target;
    var image = displayAdsScreenWatch.defaultSettingModel?.defaultReachedAsset.target?.image.target;
    if (video == null && (image == null || audio == null)) return false;
    return true;
  }
}
