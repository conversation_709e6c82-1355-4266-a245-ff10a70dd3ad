import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:odigo_offline/ui/utils/theme/theme.dart';
import 'package:flutter/material.dart';

// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {


  /// Returns a date in "01/01/2000 01:00 AM"
  String get toFormattedDateTime {
    final day = this.day.toString().padLeft(2, '0');
    final month = this.month.toString().padLeft(2, '0');
    final year = this.year.toString();
    final hour = this.hour.toString().padLeft(2, '0');
    final minute = this.minute.toString().padLeft(2, '0');
    final period = this.hour < 12 ? 'AM' : 'PM';

    return '$day/$month/$year $hour:$minute $period';
  }

  String get getFormattedDateTime {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    final day = twoDigits(this.day);
    final month = twoDigits(this.month);
    final year = this.year.toString();
    final hour = twoDigits(this.hour);
    final minute = twoDigits(this.minute);

    return '$day/$month/$year $hour:$minute';
  }

  DateTime get today {
    return DateTime(year, month, day);
  }

  DateTime get getMonth {
    return DateTime(year, month);
  }

  DateTime get yearOnly {
    return DateTime(year);
  }

  DateTime get nextMonth {
    return DateTime(year, (month + 1));
  }

  DateTime get previousMonth {
    return DateTime(year, (month - 1));
  }

  String toStringWithCustomDate(String outputFormat) {
    return DateFormat(outputFormat).format(this);
  }

  ///Returns a Date as a String in '01/01/2000' format
  String get dateOnly {
    return '${day.toString().padLeft(2, '0')}/${month.toString().padLeft(2, '0')}/$year ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  }

  ///Returns a Date as a DateTime in '01/01/2000' format
  DateTime get date {
    return DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
  }

  String get time {
    String formattedTime = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  bool isTimeBeforeOrEqual(DateTime afterDate) {
    var beforeTime = DateFormat('HH:mm:ss').parse(time);
    var afterTime = DateFormat('HH:mm:ss').parse(afterDate.time);
    return beforeTime.isDateBeforeOrEqual(afterTime);
  }

  bool isTimeAfterOrEqual(DateTime afterDate) {
    var beforeTime = DateFormat('HH:mm:ss').parse(time);
    var afterTime = DateFormat('HH:mm:ss').parse(afterDate.time);
    return beforeTime.isDateAfterOrEqual(afterTime);
  }

  bool isTimeBefore(DateTime afterDate) {
    var beforeTime = DateFormat('HH:mm:ss').parse(time);
    var afterTime = DateFormat('HH:mm:ss').parse(afterDate.time);
    return beforeTime.isBefore(afterTime);
  }

  bool isTimeAfter(DateTime afterDate) {
    var beforeTime = DateFormat('HH:mm:ss').parse(time);
    var afterTime = DateFormat('HH:mm:ss').parse(afterDate.time);
    return beforeTime.isAfter(afterTime);
  }

  ///Returns Hour and Time in 12 hour format as String From DateTime Object
  ///Format: '12 PM'
  String get hourOnly {
    int hours = hour;
    String period = (hours >= 12) ? 'PM' : 'AM';

    ///Convert to 12 hour format
    if (hours > 12) {
      hours -= 12;
    } else if (hours == 0) {
      hours = 12;
    }
    String formattedTime = '${hours.toString().padLeft(2, '0')}:00 $period';
    return formattedTime;
  }

  ///Returns Hour and Time in 12 hour format as String From DateTime Object
  ///Format: '12:00:01 PM'
  String completeTime(int seconds) {
    int hours = hour;
    String period = (hours >= 12) ? 'PM' : 'AM';

    ///Convert to 12 hour format
    if (hours > 12) {
      hours -= 12;
    } else if (hours == 0) {
      hours = 12;
    }

    String minutes = '';
    if (seconds % 2 == 0) {
      minutes = '${(seconds ~/ 2).toString().padLeft(2, '0')}:00';
    } else {
      minutes = '${(seconds ~/ 2).toString().padLeft(2, '0')}:30';
    }

    String formattedTime = '${hours.toString().padLeft(2, '0')}:$minutes $period';
    return formattedTime;
  }

  ///Format: '12:00'
  String get hourMinuteOnly {
    String formattedTime = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  ///Format: '12:00:00'
  String get hourMinuteSecondOnly {
    String formattedTime = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  ///Get Days in month
  int get daysInMonth {
    var firstDayThisMonth = DateTime(year, month, day);
    var firstDayNextMonth = DateTime(firstDayThisMonth.year, firstDayThisMonth.month + 1, firstDayThisMonth.day);
    return firstDayNextMonth.difference(firstDayThisMonth).inDays;
  }

  bool isDateBeforeOrEqual(DateTime secondDate) {
    int startSeconds = (hour * 3600) + (minute * 60) + second;
    int endSeconds = (secondDate.hour * 3600) + (secondDate.minute * 60) + secondDate.second;
    return startSeconds <= endSeconds;
  }

  bool isDateAfterOrEqual(DateTime secondDate) {
    int startSeconds = (hour * 3600) + (minute * 60) + second;
    int endSeconds = (secondDate.hour * 3600) + (secondDate.minute * 60) + secondDate.second;
    return startSeconds >= endSeconds;
  }

  bool get isToday {
    return (day == DateTime.now().day && month == DateTime.now().month && year == DateTime.now().year);
  }

  bool isTimeEqual(DateTime dateTime) {
    return (hour == dateTime.hour && minute == dateTime.minute && second == dateTime.second);
  }

  int calculateSlotsBetweenGivenTime(DateTime dateTime) {
    int differenceSeconds = difference(dateTime).inSeconds;
    return (differenceSeconds ~/ constantSlotDuration.inSeconds);
  }
}

extension TimeExtension on TimeOfDay {
  bool isEqual(TimeOfDay time) {
    return this == time;
  }

  bool isBefore(TimeOfDay time) {
    int startSeconds = (hour * 3600) + (minute * 60);
    int endSeconds = (time.hour * 3600) + (time.minute * 60);
    return startSeconds < endSeconds;
  }

  bool isAfter(TimeOfDay time) {
    int startSeconds = (hour * 3600) + (minute * 60);
    int endSeconds = (time.hour * 3600) + (time.minute * 60);
    return startSeconds > endSeconds;
  }
}
