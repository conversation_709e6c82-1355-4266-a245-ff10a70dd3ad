

import 'dart:convert';

import 'package:basic_utils/basic_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:odigo_offline/framework/utils/encryption/encryption_extension.dart';
import 'package:pointycastle/export.dart';

extension StringExtension on String {
  String get replaceAllSpace => replaceAll(' ', '').replaceAll('\n', '');
}

showLogMyLog(String str){
  debugPrint(str, wrapWidth: 500);
}

class EncryptionHelper {
  // Private constructor for singleton pattern
  EncryptionHelper._();

  /// Singleton Instance for [EncryptionHelper] Class
  static EncryptionHelper instance = EncryptionHelper._();

  /// [PKCS1Encoder] for RSA Encryption
  PKCS1Encoding encrypter = PKCS1Encoding(RSAEngine());

  /// [PKCS1Encoder] for RSA Decryption
  PKCS1Encoding decrypter = PKCS1Encoding(RSAEngine());

  /// [RSAPublicKey] for Encryption
  RSAPublicKey? rsaPublicKey;

  /// [RSAPrivateKey] for Decryption
  RSAPrivateKey? rsaPrivateKey;

  /// Initialize [RSAPublicKey] for encryption
  Future<void> initRSAEncryptor(String publicKey) async {
    rsaPublicKey = CryptoUtils.rsaPublicKeyFromPem(publicKey.publicKeyToPem);
    encrypter.init(true, PublicKeyParameter<RSAPublicKey>(rsaPublicKey!));
  }

  /// Initialize [RSAPrivateKey] for decryption
  Future<void> initRSADecrypt(String privateKey) async {
    rsaPrivateKey = CryptoUtils.rsaPrivateKeyFromPem(privateKey.privateKeyToPem);
    decrypter.init(false, PrivateKeyParameter<RSAPrivateKey>(rsaPrivateKey!));
  }

  // String? aesSecret; // The secret key for AES
  // String? aesSalt; // The salt for AES key derivation
  // SecretKey? secretKey; // Derived AES secret key
  PaddedBlockCipherImpl? encryptCipher; // Cipher for AES encryption
  PaddedBlockCipherImpl? decryptCipher; // Cipher for AES decryption

  /// Initialize AES keys using plain secret and salt
  String initAESKeys(String aesSecret, String aesSalt) {
    // this.aesSecret = aesSecret; // Store the AES secret
    // this.aesSalt = aesSalt; // Store the AES salt
    final secretKey = getKeyFromPlainSecretKeyAndSalt(aesSecret, aesSalt); // Derive the AES key
    return secretKey.base64;
  }

  /// Initialize AES keys using plain secret and salt
  void initSplashAESKeys(String key, String aesSalt) {
    // this.aesSalt = aesSalt;
    // final secretKey = SecretKey(base64Decode(key));
  }

  /// Function to perform AES encryption with a given plain text
  String encryptAES(String plainText, String sessionSecretKey, String aesSalt) {
    try {
      showLogMyLog('ENCRYPT AES STARTED');
      final key = base64Decode(sessionSecretKey); // Get the derived AES key
      final iv = base64Decode(aesSalt); // Decode the salt to use as IV
      // Set up the encryption cipher with padding and CBC mode
      encryptCipher = PaddedBlockCipherImpl(PKCS7Padding(), CBCBlockCipher(AESEngine()))..init(true, PaddedBlockCipherParameters<ParametersWithIV<KeyParameter>, Null>(ParametersWithIV<KeyParameter>(KeyParameter(key), iv), null));

      showLogMyLog('Plain Text <<<\n $plainText \n>>>');

      // Convert plain text to bytes
      final plainTextBytes = utf8.encode(plainText.replaceAllSpace);

      // Encrypt the data
      final encryptedBytes = encryptCipher!.process(Uint8List.fromList(plainTextBytes));

      // Convert the encrypted data to Base64 for easy readability
      String encryptedData = base64.encode(encryptedBytes);
      showLogMyLog('Encrypted Data <<<\n $encryptedData \n>>>');
      return encryptedData;
    } catch (e) {
      showLogMyLog('Encrypt AES Failed  $e');
      return '';
    }
  }

  /// Function to perform AES decryption with a given encrypted text
  String decryptAES(String encryptedTextBase64, String sessionSecretKey, String aesSalt) {
    try {
      showLogMyLog('DECRYPT AES STARTED');
      final key = base64Decode(sessionSecretKey); // Get the derived AES key
      final iv = base64Decode(aesSalt); // Decode the salt to use as IV
      // Set up the decryption cipher with padding and CBC mode
      decryptCipher = PaddedBlockCipherImpl(PKCS7Padding(), CBCBlockCipher(AESEngine()))..init(false, PaddedBlockCipherParameters<ParametersWithIV<KeyParameter>, Null>(ParametersWithIV<KeyParameter>(KeyParameter(key), iv), null));
      // Decode the Base64 encrypted text to get bytes
      final encryptedBytes = base64.decode(encryptedTextBase64.replaceAllSpace);
      // Decrypt the data
      final decryptedBytes = decryptCipher!.process(Uint8List.fromList(encryptedBytes));
      // Convert decrypted bytes to string (UTF-8 format)
      String decryptedData = utf8.decode(decryptedBytes);
      showLogMyLog('Decrypted Data Data <<<\n $decryptedData \n>>>');
      return decryptedData.replaceAllSpace;
    } catch (e) {
      showLogMyLog('Decrypt AES Failed  $e');
      return '';
    }
  }

  /// Function to encrypt a string using RSA
  Future<String> encryptRSA(String value) async {
    Uint8List output = encrypter.process(utf8.encode(value)); // Encrypt the input value
    return base64Encode(output); // Return the encrypted value in Base64 format
  }

  /// Function to decrypt a string using RSA
  Future<String> decryptRSA(String value) async {
    Uint8List output = EncryptionHelper.instance.decrypter.process(base64Decode(value)); // Decrypt the input value
    return utf8.decode(output); // Return the decrypted value as a string
  }

  /// Function to generate a key-value pair (public and private keys)
  Future<(String, String)> generateKeyValuePair() async {
    var (publicKey, privateKey) = generateKey(); // Generate RSA key pair
    await Future.delayed(const Duration(milliseconds: 500)); // Optional delay
    return (publicKey, privateKey); // Return the key pair
  }

  /// Function to generate RSA key pair
  (String, String) generateKey() {
    var keyPair = CryptoUtils.generateRSAKeyPair(keySize: 2048); // Generate RSA key pair

    // Extract public and private keys
    RSAPublicKey publicKey = keyPair.publicKey as RSAPublicKey;
    RSAPrivateKey privateKey = keyPair.privateKey as RSAPrivateKey;
    String publicKeyStr = CryptoUtils.encodeRSAPublicKeyToPem(publicKey); // Convert public key to PEM format
    String privateKeyStr = CryptoUtils.encodeRSAPrivateKeyToPem(privateKey); // Convert private key to PEM format
    return (publicKeyStr.keyFromPem, privateKeyStr.keyFromPem); // Return the PEM formatted keys
  }

  /// Function to generate a SecretKey from a plain secret key and salt
  SecretKey getKeyFromPlainSecretKeyAndSalt(String plainSecretKey, String salt) {
    // Convert salt to bytes
    final saltBytes = utf8.encode(salt);

    // Create a PBKDF2 key derivation function with HMAC-SHA256
    final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64))..init(Pbkdf2Parameters(Uint8List.fromList(saltBytes), 65536, 32)); // Initialize with salt and iterations

    // Derive the key
    final derivedKey = pbkdf2.process(utf8.encode(plainSecretKey));

    // Create and return the SecretKey
    return SecretKey(Uint8List.fromList(derivedKey));
  }
}

/// Class representing a SecretKey
class SecretKey {
  final Uint8List key; // The derived key bytes

  SecretKey(this.key);

  // Method to convert the key to Base64 format
  String get base64 => base64Encode(key);

  // Method to convert the key to hexadecimal format
  String get hex => key.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
}
