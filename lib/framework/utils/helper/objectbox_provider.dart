import 'package:flutter/foundation.dart';
import 'package:odigo_offline/ui/utils/const/app_constants.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:odigo_offline/objectbox.g.dart';

class ObjectBoxProvider {
  ObjectBoxProvider._create(this.store) {
    // Add any additional setup code, e.g. build queries.

    Future.delayed(const Duration(seconds: 00), () {
      if (kDebugMode) {
        if (Admin.isAvailable()) {
          admin = Admin(store!);
        }
      }
    });
  }

  ObjectBoxProvider._();

  static ObjectBoxProvider objectBox = ObjectBoxProvider._();

  /// Storing Object box path
  Store? store;
  late final Admin admin;

  String objectBoxProvider = 'odigo-tv';

  Future<void> create() async {
    await close();
    final docsDir = applicationDirectory;
    final store = await openStore(
      directory: path.join(docsDir.path ?? '', objectBoxProvider),
    );
    showLog('Object box database path : ${path.join(docsDir.path ?? '', objectBoxProvider)}');
    objectBox = ObjectBoxProvider._create(store);
  }

  Future<void> close() async {
    if (!(store?.isClosed() ?? true)) {
      store?.close();
      print("ObjectBox store closed successfully.");
    }
  }
}
